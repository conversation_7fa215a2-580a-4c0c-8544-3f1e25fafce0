# 信件助理后端服务

基于 Spring Boot 3.x + SQLite 的信件助理后端服务，为小程序提供完整的 API 支持。

## 技术栈

- **框架**: Spring Boot 3.2.0
- **数据库**: SQLite 3
- **ORM**: Spring Data JPA + Hibernate
- **安全**: Spring Security
- **构建工具**: Maven
- **Java版本**: 17

## 功能特性

### 🔐 用户管理
- 用户注册/登录
- 用户信息管理
- 邀请码系统
- 余额管理

### ✉️ 信件服务
- 信件创建和管理
- 支付处理
- 物流跟踪
- 状态管理

### 📮 明信片服务
- 明信片模板管理
- 明信片创建和发送
- 个性化定制

### 📔 心语日记
- 日记创建和管理
- 心情记录
- 私密性设置

### 🛒 商城功能
- 商品管理
- 订单处理
- 库存管理

## 项目结构

```
mail-boot/
├── src/main/java/com/mailapp/
│   ├── MailBootApplication.java    # 启动类
│   ├── entity/                     # 实体类
│   │   ├── BaseEntity.java
│   │   ├── User.java
│   │   ├── Letter.java
│   │   ├── Postcard.java
│   │   ├── Diary.java
│   │   ├── Product.java
│   │   └── Order.java
│   ├── repository/                 # 数据访问层
│   │   ├── UserRepository.java
│   │   ├── LetterRepository.java
│   │   ├── PostcardRepository.java
│   │   └── DiaryRepository.java
│   ├── service/                    # 业务逻辑层
│   │   ├── UserService.java
│   │   └── LetterService.java
│   ├── controller/                 # 控制器层
│   │   ├── UserController.java
│   │   └── LetterController.java
│   ├── config/                     # 配置类
│   │   ├── WebConfig.java
│   │   └── SecurityConfig.java
│   └── common/                     # 通用类
│       ├── Result.java
│       └── PageResult.java
├── src/main/resources/
│   └── application.yml             # 配置文件
├── pom.xml                         # Maven配置
└── README.md                       # 项目说明
```

## 快速开始

### 环境要求
- JDK 17+
- Maven 3.6+

### 运行步骤

1. **克隆项目**
   ```bash
   cd mail-boot
   ```

2. **编译项目**
   ```bash
   mvn clean compile
   ```

3. **运行项目**
   ```bash
   mvn spring-boot:run
   ```

4. **访问服务**
   - API 基础地址: http://localhost:8080/api
   - 数据库文件: mail.db (自动创建)

## API 接口

### 用户相关
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `GET /api/user/{id}` - 获取用户信息
- `PUT /api/user/{id}` - 更新用户信息
- `GET /api/user/{id}/invites` - 获取邀请列表
- `GET /api/user/{id}/invite-stats` - 获取邀请统计

### 信件相关
- `POST /api/letter` - 创建信件
- `GET /api/letter/user/{userId}` - 获取用户信件列表
- `GET /api/letter/{id}` - 获取信件详情
- `POST /api/letter/{id}/pay` - 支付信件
- `GET /api/letter/tracking/{trackingNumber}` - 查询物流
- `GET /api/letter/user/{userId}/count` - 统计信件数量

## 数据库设计

### 用户表 (users)
- id: 主键
- username: 用户名
- password: 密码
- nickname: 昵称
- phone: 手机号
- email: 邮箱
- avatar: 头像
- balance: 余额
- invite_code: 邀请码
- inviter_id: 邀请人ID
- status: 状态
- created_at: 创建时间
- updated_at: 更新时间

### 信件表 (letters)
- id: 主键
- user_id: 用户ID
- title: 标题
- content: 内容
- sender_name: 寄件人姓名
- sender_address: 寄件人地址
- receiver_name: 收件人姓名
- receiver_address: 收件人地址
- delivery_type: 配送方式
- price: 价格
- status: 状态
- tracking_number: 快递单号
- created_at: 创建时间

## 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    url: jdbc:sqlite:mail.db
    driver-class-name: org.sqlite.JDBC
```

### 跨域配置
```yaml
cors:
  allowed-origins: 
    - http://localhost:3000
    - http://127.0.0.1:3000
```

## 开发建议

1. **API测试**: 使用 Postman 或类似工具测试API接口
2. **数据库查看**: 使用 SQLite Browser 查看数据库内容
3. **日志查看**: 查看 logs/mail-boot.log 文件
4. **错误处理**: 所有API都返回统一的Result格式

## 部署说明

### 打包应用
```bash
mvn clean package
```

### 运行JAR包
```bash
java -jar target/mail-boot-1.0.0.jar
```

### Docker部署（可选）
```dockerfile
FROM openjdk:17-jdk-slim
COPY target/mail-boot-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 注意事项

1. **数据库**: SQLite数据库文件会自动创建在项目根目录
2. **安全**: 当前配置为开发环境，生产环境需要加强安全配置
3. **性能**: SQLite适合开发和小规模应用，大规模应用建议使用MySQL/PostgreSQL
4. **日志**: 日志文件保存在 logs/ 目录下

## 许可证

MIT License
