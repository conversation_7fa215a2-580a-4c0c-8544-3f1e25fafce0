# 信件助理小程序

基于 uniapp + vite + bun.js + vue3.x 开发的信件助理小程序，提供线上寄信、明信片、心语日记等功能。

## 功能特性

### 🏠 首页
- 轮播图展示
- 主要功能入口（寄信、明信片）
- 其他功能网格（心语日记、回信物流、回家倒计时、代收信箱）
- 客服联系按钮

### ✉️ 寄信功能
- 收件人信息填写
- 寄件人信息填写
- 信件内容编辑
- 配送方式选择（标准/加急）

### 📮 明信片功能
- 明信片模板选择
- 实时预览效果
- 正面文字编辑
- 背面内容编辑
- 收件人信息填写

### 📔 心语日记
- 日记列表展示
- 心情记录
- 日记编辑功能

### 📦 回信物流
- 快递单号查询
- 物流轨迹展示
- 历史查询记录

### ⏰ 回家倒计时
- 回家时间设置
- 实时倒计时显示
- 心情记录功能
- 心情历史查看

### 📬 代收信箱
- 信件统计展示
- 信件分类筛选
- 信件列表查看
- 未读消息提醒

### 🛒 商城
- 商品分类浏览
- 商品搜索功能
- 商品详情查看

### 🎁 邀请有礼
- 邀请码生成
- 分享功能
- 邀请记录查看
- 奖励规则说明

### 👤 个人中心
- 用户信息管理
- 数据统计展示
- 功能菜单导航
- 设置管理

## 技术栈

- **框架**: uniapp
- **构建工具**: vite
- **包管理器**: bun.js
- **前端框架**: vue3.x
- **状态管理**: pinia
- **语言**: TypeScript
- **样式**: CSS3

## 项目结构

```
mail-uniapp/
├── src/
│   ├── pages/           # 页面文件
│   │   ├── index/       # 首页
│   │   ├── letter/      # 寄信页面
│   │   ├── postcard/    # 明信片页面
│   │   ├── diary/       # 心语日记
│   │   ├── logistics/   # 回信物流
│   │   ├── countdown/   # 回家倒计时
│   │   ├── mailbox/     # 代收信箱
│   │   ├── store/       # 商城
│   │   ├── gift/        # 邀请有礼
│   │   └── profile/     # 个人中心
│   ├── static/          # 静态资源
│   ├── App.vue          # 应用入口
│   ├── main.ts          # 主入口文件
│   ├── manifest.json    # 应用配置
│   └── pages.json       # 页面配置
├── package.json         # 项目配置
├── vite.config.ts       # Vite 配置
├── tsconfig.json        # TypeScript 配置
└── README.md           # 项目说明
```

## 开发环境

### 环境要求
- Node.js >= 16.0.0
- Bun >= 1.0.0
- HBuilderX 或 VSCode

### 安装依赖

使用 bun 安装依赖：
```bash
bun install
```

或使用 npm：
```bash
npm install
```

### 开发运行

#### 微信小程序
```bash
bun run dev:mp-weixin
```

#### H5
```bash
bun run dev:h5
```

#### App
```bash
bun run dev:app
```

### 构建打包

#### 微信小程序
```bash
bun run build:mp-weixin
```

#### H5
```bash
bun run build:h5
```

#### App
```bash
bun run build:app
```

## 部署说明

### 微信小程序
1. 使用 HBuilderX 或微信开发者工具打开项目
2. 配置小程序 AppID
3. 上传代码到微信后台
4. 提交审核

### H5
1. 构建 H5 版本
2. 将 dist 目录部署到服务器
3. 配置域名和 HTTPS

## 注意事项

1. **图片资源**: 项目中的图片路径需要替换为实际的图片文件
2. **API 接口**: 需要根据实际后端接口调整数据请求
3. **小程序配置**: 需要在 manifest.json 中配置正确的 AppID
4. **权限配置**: 根据功能需求配置相应的小程序权限

## 开发建议

1. 使用 TypeScript 进行类型检查
2. 遵循 Vue3 Composition API 规范
3. 保持代码风格一致
4. 及时更新依赖版本
5. 做好错误处理和用户体验优化

## 许可证

MIT License
