# 图片占位符说明

由于项目中引用了一些图片资源，但实际图片文件还未添加，您需要准备以下图片：

## 轮播图
- `banner1.jpg` - 首页轮播图 (750x400px)

## 功能图标 (64x64px)
- `icons/letter.png` - 寄信图标
- `icons/postcard.png` - 明信片图标  
- `icons/diary.png` - 心语日记图标
- `icons/logistics.png` - 回信物流图标
- `icons/countdown.png` - 回家倒计时图标
- `icons/mailbox.png` - 代收信箱图标
- `icons/service.png` - 客服图标
- `icons/search.png` - 搜索图标
- `icons/wechat.png` - 微信图标
- `icons/moments.png` - 朋友圈图标
- `icons/qq.png` - QQ图标
- `icons/qzone.png` - QQ空间图标

## 底部导航图标 (64x64px)
- `tabbar/home.png` - 首页图标（未选中）
- `tabbar/home-active.png` - 首页图标（选中）
- `tabbar/store.png` - 商城图标（未选中）
- `tabbar/store-active.png` - 商城图标（选中）
- `tabbar/gift.png` - 邀请有礼图标（未选中）
- `tabbar/gift-active.png` - 邀请有礼图标（选中）
- `tabbar/profile.png` - 我的图标（未选中）
- `tabbar/profile-active.png` - 我的图标（选中）

## 明信片模板 (320x240px)
- `postcard1.jpg` - 风景明信片
- `postcard2.jpg` - 城市明信片
- `postcard3.jpg` - 花卉明信片
- `postcard4.jpg` - 动物明信片

## 商品图片 (300x300px)
- `product1.jpg` - 复古牛皮纸信纸
- `product2.jpg` - 手工花边信封
- `product3.jpg` - 风景明信片套装
- `product4.jpg` - 限量版纪念邮票
- `product5.jpg` - 钢笔墨水套装
- `product6.jpg` - 古典信纸礼盒

## 空状态图片 (200x200px)
- `empty-diary.png` - 空日记状态
- `empty-mailbox.png` - 空信箱状态
- `empty-store.png` - 空商城状态

## 其他
- `share-logo.png` - 分享logo (120x120px)
- `favicon.ico` - 网站图标 (32x32px)

## 临时解决方案

在开发阶段，您可以：
1. 使用在线图片服务（如 picsum.photos）作为占位图
2. 使用纯色背景代替图片
3. 使用 emoji 表情作为图标占位符

项目已经配置好了所有功能，只需要替换这些图片资源即可完整运行。
