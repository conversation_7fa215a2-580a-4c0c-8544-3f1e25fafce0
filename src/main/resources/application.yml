server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: mail-boot

  datasource:
    url: jdbc:sqlite:mail.db
    driver-class-name: org.sqlite.JDBC
    username:
    password:

  jpa:
    database-platform: org.hibernate.dialect.SQLiteDialect
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.SQLiteDialect
  
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
  
  security:
    user:
      name: admin
      password: admin123

# 自定义配置
mail:
  jwt:
    secret: mailAppSecretKey2024ForJWTTokenGeneration
    expiration: 86400000 # 24小时
  
  upload:
    path: ./uploads/
    max-file-size: 10MB
    max-request-size: 50MB
  
  sms:
    enabled: false
    app-key: your-sms-app-key
    app-secret: your-sms-app-secret
  
  logistics:
    enabled: true
    api-key: your-logistics-api-key

# 日志配置
logging:
  level:
    com.mailapp: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/mail-boot.log

# 跨域配置
cors:
  allowed-origins: 
    - http://localhost:3000
    - http://127.0.0.1:3000
    - http://**************:3000
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers: "*"
  allow-credentials: true
