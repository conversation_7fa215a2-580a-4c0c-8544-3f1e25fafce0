package com.mailapp.service;

import com.mailapp.common.PageResult;
import com.mailapp.entity.Letter;
import com.mailapp.repository.LetterRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 信件服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LetterService {

    private final LetterRepository letterRepository;

    /**
     * 创建信件
     */
    @Transactional
    public Letter createLetter(Letter letter) {
        // 设置价格
        if ("EXPRESS".equals(letter.getDeliveryType())) {
            letter.setPrice(new BigDecimal("15.00"));
        } else {
            letter.setPrice(new BigDecimal("8.00"));
        }
        
        letter.setStatus("PENDING");
        return letterRepository.save(letter);
    }

    /**
     * 获取用户信件列表
     */
    public PageResult<Letter> getUserLetters(Long userId, String status, int page, int size) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<Letter> letterPage;
        
        if (status != null && !status.trim().isEmpty()) {
            letterPage = letterRepository.findByUserIdAndStatusAndDeletedFalse(userId, status, pageable);
        } else {
            letterPage = letterRepository.findByUserIdAndDeletedFalse(userId, pageable);
        }
        
        return PageResult.of(letterPage);
    }

    /**
     * 根据ID查找信件
     */
    public Optional<Letter> findById(Long id) {
        return letterRepository.findById(id).filter(letter -> !letter.getDeleted());
    }

    /**
     * 支付信件
     */
    @Transactional
    public Letter payLetter(Long letterId, String paymentMethod) {
        Optional<Letter> letterOpt = findById(letterId);
        if (letterOpt.isEmpty()) {
            throw new RuntimeException("信件不存在");
        }

        Letter letter = letterOpt.get();
        if (!"PENDING".equals(letter.getStatus())) {
            throw new RuntimeException("信件状态不正确");
        }

        letter.setStatus("PAID");
        letter.setPaymentMethod(paymentMethod);
        letter.setPaymentTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        return letterRepository.save(letter);
    }

    /**
     * 发货
     */
    @Transactional
    public Letter shipLetter(Long letterId) {
        Optional<Letter> letterOpt = findById(letterId);
        if (letterOpt.isEmpty()) {
            throw new RuntimeException("信件不存在");
        }

        Letter letter = letterOpt.get();
        if (!"PAID".equals(letter.getStatus())) {
            throw new RuntimeException("信件状态不正确");
        }

        letter.setStatus("SHIPPED");
        letter.setTrackingNumber(generateTrackingNumber());
        letter.setShippedTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        return letterRepository.save(letter);
    }

    /**
     * 确认送达
     */
    @Transactional
    public Letter deliverLetter(Long letterId) {
        Optional<Letter> letterOpt = findById(letterId);
        if (letterOpt.isEmpty()) {
            throw new RuntimeException("信件不存在");
        }

        Letter letter = letterOpt.get();
        if (!"SHIPPED".equals(letter.getStatus())) {
            throw new RuntimeException("信件状态不正确");
        }

        letter.setStatus("DELIVERED");
        letter.setDeliveredTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        return letterRepository.save(letter);
    }

    /**
     * 根据快递单号查询
     */
    public Optional<Letter> findByTrackingNumber(String trackingNumber) {
        return letterRepository.findByTrackingNumberAndDeletedFalse(trackingNumber);
    }

    /**
     * 统计用户信件数量
     */
    public Long countUserLetters(Long userId) {
        return letterRepository.countByUserId(userId);
    }

    /**
     * 获取需要处理的信件
     */
    public List<Letter> getPendingLetters() {
        return letterRepository.findByStatusIn(List.of("PAID", "PROCESSING"));
    }

    /**
     * 生成快递单号
     */
    private String generateTrackingNumber() {
        return "ML" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 6).toUpperCase();
    }
}
