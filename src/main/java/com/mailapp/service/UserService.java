package com.mailapp.service;

import com.mailapp.common.PageResult;
import com.mailapp.entity.User;
import com.mailapp.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.UUID;

/**
 * 用户服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    /**
     * 用户注册
     */
    @Transactional
    public User register(String username, String password, String phone, String inviteCode) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsernameAndDeletedFalse(username)) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查手机号是否已存在
        if (phone != null && userRepository.existsByPhoneAndDeletedFalse(phone)) {
            throw new RuntimeException("手机号已存在");
        }

        User user = new User();
        user.setUsername(username);
        user.setPassword(passwordEncoder.encode(password));
        user.setPhone(phone);
        user.setNickname(username);
        user.setInviteCode(generateInviteCode());
        user.setBalance(BigDecimal.ZERO);
        user.setLevel("普通用户");
        user.setStatus("ACTIVE");
        user.setLoginCount(0);

        // 处理邀请码
        if (inviteCode != null && !inviteCode.trim().isEmpty()) {
            Optional<User> inviter = userRepository.findByInviteCodeAndDeletedFalse(inviteCode);
            if (inviter.isPresent()) {
                user.setInviterId(inviter.get().getId());
                // 给邀请人奖励
                giveInviteReward(inviter.get());
            }
        }

        return userRepository.save(user);
    }

    /**
     * 用户登录
     */
    public User login(String username, String password) {
        Optional<User> userOpt = userRepository.findByUsernameAndDeletedFalse(username);
        if (userOpt.isEmpty()) {
            throw new RuntimeException("用户不存在");
        }

        User user = userOpt.get();
        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        if (!"ACTIVE".equals(user.getStatus())) {
            throw new RuntimeException("账户已被禁用");
        }

        // 更新登录信息
        user.setLastLoginTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        user.setLoginCount(user.getLoginCount() + 1);
        userRepository.save(user);

        return user;
    }

    /**
     * 根据ID查找用户
     */
    public Optional<User> findById(Long id) {
        return userRepository.findById(id).filter(user -> !user.getDeleted());
    }

    /**
     * 更新用户信息
     */
    @Transactional
    public User updateUser(User user) {
        return userRepository.save(user);
    }

    /**
     * 获取用户邀请列表
     */
    public PageResult<User> getInviteList(Long userId, int page, int size) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<User> userPage = userRepository.findByInviterId(userId, pageable);
        return PageResult.of(userPage);
    }

    /**
     * 统计邀请人数
     */
    public Long countInvites(Long userId) {
        return userRepository.countByInviterId(userId);
    }

    /**
     * 生成邀请码
     */
    private String generateInviteCode() {
        String code;
        do {
            code = "MAIL" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        } while (userRepository.findByInviteCodeAndDeletedFalse(code).isPresent());
        return code;
    }

    /**
     * 给邀请人奖励
     */
    private void giveInviteReward(User inviter) {
        // 给邀请人增加余额奖励
        BigDecimal reward = new BigDecimal("5.00");
        inviter.setBalance(inviter.getBalance().add(reward));
        userRepository.save(inviter);
        log.info("给用户 {} 发放邀请奖励 {} 元", inviter.getUsername(), reward);
    }
}
