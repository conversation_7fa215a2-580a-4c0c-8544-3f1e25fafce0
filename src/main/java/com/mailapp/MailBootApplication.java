package com.mailapp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 信件助理应用启动类
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableAsync
@EnableScheduling
public class MailBootApplication {

    public static void main(String[] args) {
        SpringApplication.run(MailBootApplication.class, args);
        System.out.println("=================================");
        System.out.println("信件助理后端服务启动成功！");
        System.out.println("API 文档地址: http://localhost:8080/api");
        System.out.println("=================================");
    }
}
