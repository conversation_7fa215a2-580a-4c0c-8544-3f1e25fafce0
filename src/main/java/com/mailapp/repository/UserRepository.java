package com.mailapp.repository;

import com.mailapp.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 用户数据访问层
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsernameAndDeletedFalse(String username);

    /**
     * 根据手机号查找用户
     */
    Optional<User> findByPhoneAndDeletedFalse(String phone);

    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmailAndDeletedFalse(String email);

    /**
     * 根据邀请码查找用户
     */
    Optional<User> findByInviteCodeAndDeletedFalse(String inviteCode);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsernameAndDeletedFalse(String username);

    /**
     * 检查手机号是否存在
     */
    boolean existsByPhoneAndDeletedFalse(String phone);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmailAndDeletedFalse(String email);

    /**
     * 根据邀请人ID查找被邀请用户
     */
    @Query("SELECT u FROM User u WHERE u.inviterId = :inviterId AND u.deleted = false")
    Page<User> findByInviterId(@Param("inviterId") Long inviterId, Pageable pageable);

    /**
     * 统计邀请人数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.inviterId = :inviterId AND u.deleted = false")
    Long countByInviterId(@Param("inviterId") Long inviterId);

    /**
     * 根据状态查找用户
     */
    Page<User> findByStatusAndDeletedFalse(String status, Pageable pageable);
}
