package com.mailapp.repository;

import com.mailapp.entity.Letter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 信件数据访问层
 */
@Repository
public interface LetterRepository extends JpaRepository<Letter, Long> {

    /**
     * 根据用户ID查找信件
     */
    Page<Letter> findByUserIdAndDeletedFalse(Long userId, Pageable pageable);

    /**
     * 根据状态查找信件
     */
    Page<Letter> findByStatusAndDeletedFalse(String status, Pageable pageable);

    /**
     * 根据用户ID和状态查找信件
     */
    Page<Letter> findByUserIdAndStatusAndDeletedFalse(Long userId, String status, Pageable pageable);

    /**
     * 根据快递单号查找信件
     */
    Optional<Letter> findByTrackingNumberAndDeletedFalse(String trackingNumber);

    /**
     * 统计用户信件数量
     */
    @Query("SELECT COUNT(l) FROM Letter l WHERE l.userId = :userId AND l.deleted = false")
    Long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户各状态信件数量
     */
    @Query("SELECT l.status, COUNT(l) FROM Letter l WHERE l.userId = :userId AND l.deleted = false GROUP BY l.status")
    List<Object[]> countByUserIdGroupByStatus(@Param("userId") Long userId);

    /**
     * 查找需要处理的信件
     */
    @Query("SELECT l FROM Letter l WHERE l.status IN :statuses AND l.deleted = false ORDER BY l.createdAt ASC")
    List<Letter> findByStatusIn(@Param("statuses") List<String> statuses);
}
