package com.mailapp.repository;

import com.mailapp.entity.Diary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 心语日记数据访问层
 */
@Repository
public interface DiaryRepository extends JpaRepository<Diary, Long> {

    /**
     * 根据用户ID查找日记
     */
    Page<Diary> findByUserIdAndDeletedFalse(Long userId, Pageable pageable);

    /**
     * 根据用户ID和心情查找日记
     */
    Page<Diary> findByUserIdAndMoodAndDeletedFalse(Long userId, String mood, Pageable pageable);

    /**
     * 统计用户日记数量
     */
    @Query("SELECT COUNT(d) FROM Diary d WHERE d.userId = :userId AND d.deleted = false")
    Long countByUserId(@Param("userId") Long userId);

    /**
     * 根据关键词搜索日记
     */
    @Query("SELECT d FROM Diary d WHERE d.userId = :userId AND (d.title LIKE %:keyword% OR d.content LIKE %:keyword%) AND d.deleted = false")
    Page<Diary> searchByKeyword(@Param("userId") Long userId, @Param("keyword") String keyword, Pageable pageable);
}
