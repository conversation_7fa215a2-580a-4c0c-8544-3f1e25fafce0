package com.mailapp.repository;

import com.mailapp.entity.Postcard;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 明信片数据访问层
 */
@Repository
public interface PostcardRepository extends JpaRepository<Postcard, Long> {

    /**
     * 根据用户ID查找明信片
     */
    Page<Postcard> findByUserIdAndDeletedFalse(Long userId, Pageable pageable);

    /**
     * 根据状态查找明信片
     */
    Page<Postcard> findByStatusAndDeletedFalse(String status, Pageable pageable);

    /**
     * 根据用户ID和状态查找明信片
     */
    Page<Postcard> findByUserIdAndStatusAndDeletedFalse(Long userId, String status, Pageable pageable);

    /**
     * 根据快递单号查找明信片
     */
    Optional<Postcard> findByTrackingNumberAndDeletedFalse(String trackingNumber);

    /**
     * 统计用户明信片数量
     */
    @Query("SELECT COUNT(p) FROM Postcard p WHERE p.userId = :userId AND p.deleted = false")
    Long countByUserId(@Param("userId") Long userId);
}
