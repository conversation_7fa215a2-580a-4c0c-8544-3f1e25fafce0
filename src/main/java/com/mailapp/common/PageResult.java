package com.mailapp.common;

import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 分页响应结果
 */
@Data
public class PageResult<T> {
    
    private List<T> records;
    private Long total;
    private Integer size;
    private Integer current;
    private Integer pages;

    public PageResult() {}

    public PageResult(List<T> records, Long total, Integer size, Integer current) {
        this.records = records;
        this.total = total;
        this.size = size;
        this.current = current;
        this.pages = (int) Math.ceil((double) total / size);
    }

    /**
     * 从Spring Data Page对象创建PageResult
     */
    public static <T> PageResult<T> of(Page<T> page) {
        return new PageResult<>(
            page.getContent(),
            page.getTotalElements(),
            page.getSize(),
            page.getNumber() + 1, // Spring Data的页码从0开始，转换为从1开始
            page.getTotalPages()
        );
    }

    /**
     * 创建空的分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<>(List.of(), 0L, 10, 1);
    }
}
