package com.mailapp.controller;

import com.mailapp.common.PageResult;
import com.mailapp.common.Result;
import com.mailapp.entity.User;
import com.mailapp.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class UserController {

    private final UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<User> register(@RequestBody Map<String, String> request) {
        try {
            String username = request.get("username");
            String password = request.get("password");
            String phone = request.get("phone");
            String inviteCode = request.get("inviteCode");

            if (username == null || username.trim().isEmpty()) {
                return Result.badRequest("用户名不能为空");
            }
            if (password == null || password.trim().isEmpty()) {
                return Result.badRequest("密码不能为空");
            }

            User user = userService.register(username, password, phone, inviteCode);
            // 不返回密码
            user.setPassword(null);
            return Result.success("注册成功", user);
        } catch (Exception e) {
            log.error("用户注册失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> request) {
        try {
            String username = request.get("username");
            String password = request.get("password");

            if (username == null || username.trim().isEmpty()) {
                return Result.badRequest("用户名不能为空");
            }
            if (password == null || password.trim().isEmpty()) {
                return Result.badRequest("密码不能为空");
            }

            User user = userService.login(username, password);
            // 不返回密码
            user.setPassword(null);

            Map<String, Object> data = new HashMap<>();
            data.put("user", user);
            data.put("token", "mock-token-" + user.getId()); // 简化的token，实际应该使用JWT

            return Result.success("登录成功", data);
        } catch (Exception e) {
            log.error("用户登录失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/{id}")
    public Result<User> getUserInfo(@PathVariable Long id) {
        try {
            Optional<User> userOpt = userService.findById(id);
            if (userOpt.isEmpty()) {
                return Result.notFound();
            }

            User user = userOpt.get();
            user.setPassword(null);
            return Result.success(user);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    public Result<User> updateUser(@PathVariable Long id, @RequestBody User userUpdate) {
        try {
            Optional<User> userOpt = userService.findById(id);
            if (userOpt.isEmpty()) {
                return Result.notFound();
            }

            User user = userOpt.get();
            // 只更新允许的字段
            if (userUpdate.getNickname() != null) {
                user.setNickname(userUpdate.getNickname());
            }
            if (userUpdate.getPhone() != null) {
                user.setPhone(userUpdate.getPhone());
            }
            if (userUpdate.getEmail() != null) {
                user.setEmail(userUpdate.getEmail());
            }
            if (userUpdate.getAvatar() != null) {
                user.setAvatar(userUpdate.getAvatar());
            }
            if (userUpdate.getGender() != null) {
                user.setGender(userUpdate.getGender());
            }
            if (userUpdate.getBirthday() != null) {
                user.setBirthday(userUpdate.getBirthday());
            }
            if (userUpdate.getAddress() != null) {
                user.setAddress(userUpdate.getAddress());
            }

            User updatedUser = userService.updateUser(user);
            updatedUser.setPassword(null);
            return Result.success("更新成功", updatedUser);
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取邀请列表
     */
    @GetMapping("/{id}/invites")
    public Result<PageResult<User>> getInviteList(
            @PathVariable Long id,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            PageResult<User> result = userService.getInviteList(id, page, size);
            // 移除敏感信息
            result.getRecords().forEach(user -> user.setPassword(null));
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取邀请列表失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取邀请统计
     */
    @GetMapping("/{id}/invite-stats")
    public Result<Map<String, Object>> getInviteStats(@PathVariable Long id) {
        try {
            Long inviteCount = userService.countInvites(id);
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("total", inviteCount);
            stats.put("success", inviteCount); // 简化处理，实际应该统计成功注册的数量
            stats.put("reward", inviteCount * 5); // 每邀请一人奖励5元
            
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取邀请统计失败", e);
            return Result.error(e.getMessage());
        }
    }
}
