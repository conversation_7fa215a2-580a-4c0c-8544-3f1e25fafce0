package com.mailapp.controller;

import com.mailapp.common.PageResult;
import com.mailapp.common.Result;
import com.mailapp.entity.Letter;
import com.mailapp.service.LetterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 信件控制器
 */
@Slf4j
@RestController
@RequestMapping("/letter")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class LetterController {

    private final LetterService letterService;

    /**
     * 创建信件
     */
    @PostMapping
    public Result<Letter> createLetter(@RequestBody Letter letter) {
        try {
            // 基本验证
            if (letter.getUserId() == null) {
                return Result.badRequest("用户ID不能为空");
            }
            if (letter.getReceiverName() == null || letter.getReceiverName().trim().isEmpty()) {
                return Result.badRequest("收件人姓名不能为空");
            }
            if (letter.getReceiverAddress() == null || letter.getReceiverAddress().trim().isEmpty()) {
                return Result.badRequest("收件人地址不能为空");
            }
            if (letter.getSenderName() == null || letter.getSenderName().trim().isEmpty()) {
                return Result.badRequest("寄件人姓名不能为空");
            }

            Letter createdLetter = letterService.createLetter(letter);
            return Result.success("信件创建成功", createdLetter);
        } catch (Exception e) {
            log.error("创建信件失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取用户信件列表
     */
    @GetMapping("/user/{userId}")
    public Result<PageResult<Letter>> getUserLetters(
            @PathVariable Long userId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            PageResult<Letter> result = letterService.getUserLetters(userId, status, page, size);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取用户信件列表失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取信件详情
     */
    @GetMapping("/{id}")
    public Result<Letter> getLetterDetail(@PathVariable Long id) {
        try {
            Optional<Letter> letterOpt = letterService.findById(id);
            if (letterOpt.isEmpty()) {
                return Result.notFound();
            }
            return Result.success(letterOpt.get());
        } catch (Exception e) {
            log.error("获取信件详情失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 支付信件
     */
    @PostMapping("/{id}/pay")
    public Result<Letter> payLetter(@PathVariable Long id, @RequestBody Map<String, String> request) {
        try {
            String paymentMethod = request.get("paymentMethod");
            if (paymentMethod == null || paymentMethod.trim().isEmpty()) {
                return Result.badRequest("支付方式不能为空");
            }

            Letter letter = letterService.payLetter(id, paymentMethod);
            return Result.success("支付成功", letter);
        } catch (Exception e) {
            log.error("支付信件失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据快递单号查询物流
     */
    @GetMapping("/tracking/{trackingNumber}")
    public Result<Map<String, Object>> getLogistics(@PathVariable String trackingNumber) {
        try {
            Optional<Letter> letterOpt = letterService.findByTrackingNumber(trackingNumber);
            if (letterOpt.isEmpty()) {
                return Result.notFound();
            }

            Letter letter = letterOpt.get();
            
            // 模拟物流信息
            Map<String, Object> logistics = new HashMap<>();
            logistics.put("title", "信件包裹");
            logistics.put("number", trackingNumber);
            logistics.put("status", letter.getStatus().toLowerCase());
            
            // 模拟物流轨迹
            java.util.List<Map<String, String>> tracks = new java.util.ArrayList<>();
            
            if (letter.getDeliveredTime() != null) {
                tracks.add(Map.of(
                    "desc", "快件已送达，感谢使用信件助理",
                    "time", letter.getDeliveredTime(),
                    "location", "目的地"
                ));
            }
            
            if (letter.getShippedTime() != null) {
                tracks.add(Map.of(
                    "desc", "快件已发出，正在运输途中",
                    "time", letter.getShippedTime(),
                    "location", "分拣中心"
                ));
            }
            
            if (letter.getPaymentTime() != null) {
                tracks.add(Map.of(
                    "desc", "订单已确认，准备发货",
                    "time", letter.getPaymentTime(),
                    "location", "处理中心"
                ));
            }
            
            logistics.put("tracks", tracks);
            
            return Result.success(logistics);
        } catch (Exception e) {
            log.error("查询物流失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 统计用户信件数量
     */
    @GetMapping("/user/{userId}/count")
    public Result<Map<String, Object>> getUserLetterCount(@PathVariable Long userId) {
        try {
            Long count = letterService.countUserLetters(userId);
            Map<String, Object> result = new HashMap<>();
            result.put("total", count);
            return Result.success(result);
        } catch (Exception e) {
            log.error("统计用户信件数量失败", e);
            return Result.error(e.getMessage());
        }
    }
}
