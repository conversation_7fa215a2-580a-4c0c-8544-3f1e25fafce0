package com.mailapp.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 信件实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "letters")
public class Letter extends BaseEntity {

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "title", length = 200)
    private String title;

    @Column(name = "content", columnDefinition = "TEXT")
    private String content;

    @Column(name = "sender_name", nullable = false, length = 100)
    private String senderName;

    @Column(name = "sender_phone", length = 20)
    private String senderPhone;

    @Column(name = "sender_address", columnDefinition = "TEXT")
    private String senderAddress;

    @Column(name = "receiver_name", nullable = false, length = 100)
    private String receiverName;

    @Column(name = "receiver_phone", length = 20)
    private String receiverPhone;

    @Column(name = "receiver_address", columnDefinition = "TEXT", nullable = false)
    private String receiverAddress;

    @Column(name = "delivery_type", length = 20)
    private String deliveryType = "STANDARD"; // STANDARD, EXPRESS

    @Column(name = "price", precision = 10, scale = 2)
    private BigDecimal price;

    @Column(name = "status", length = 20)
    private String status = "PENDING"; // PENDING, PAID, PROCESSING, SHIPPED, DELIVERED, CANCELLED

    @Column(name = "tracking_number", length = 50)
    private String trackingNumber;

    @Column(name = "payment_method", length = 20)
    private String paymentMethod;

    @Column(name = "payment_time")
    private String paymentTime;

    @Column(name = "shipped_time")
    private String shippedTime;

    @Column(name = "delivered_time")
    private String deliveredTime;

    @Column(name = "remark")
    private String remark;
}
