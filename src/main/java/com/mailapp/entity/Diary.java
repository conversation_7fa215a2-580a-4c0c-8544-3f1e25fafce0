package com.mailapp.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 心语日记实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "diaries")
public class Diary extends BaseEntity {

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "title", length = 200)
    private String title;

    @Column(name = "content", columnDefinition = "TEXT", nullable = false)
    private String content;

    @Column(name = "mood", length = 20)
    private String mood; // HAPPY, SAD, ANGRY, EXCITED, THOUGHTFUL, etc.

    @Column(name = "mood_text", length = 50)
    private String moodText;

    @Column(name = "mood_emoji", length = 10)
    private String moodEmoji;

    @Column(name = "weather", length = 20)
    private String weather;

    @Column(name = "location", length = 100)
    private String location;

    @Column(name = "is_private")
    private Boolean isPrivate = true;

    @Column(name = "tags")
    private String tags; // JSON格式存储标签

    @Column(name = "images")
    private String images; // JSON格式存储图片路径
}
