package com.mailapp.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商品实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "products")
public class Product extends BaseEntity {

    @Column(name = "name", nullable = false, length = 200)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "price", precision = 10, scale = 2, nullable = false)
    private BigDecimal price;

    @Column(name = "original_price", precision = 10, scale = 2)
    private BigDecimal originalPrice;

    @Column(name = "category", length = 50)
    private String category;

    @Column(name = "image_url")
    private String imageUrl;

    @Column(name = "images", columnDefinition = "TEXT")
    private String images; // JSON格式存储多张图片

    @Column(name = "stock")
    private Integer stock = 0;

    @Column(name = "sales")
    private Integer sales = 0;

    @Column(name = "status", length = 20)
    private String status = "ACTIVE"; // ACTIVE, INACTIVE

    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    @Column(name = "is_featured")
    private Boolean isFeatured = false;

    @Column(name = "tags")
    private String tags; // JSON格式存储标签
}
