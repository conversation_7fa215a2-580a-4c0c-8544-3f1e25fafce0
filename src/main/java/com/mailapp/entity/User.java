package com.mailapp.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 用户实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "users")
public class User extends BaseEntity {

    @Column(name = "username", unique = true, nullable = false, length = 50)
    private String username;

    @Column(name = "password", nullable = false)
    private String password;

    @Column(name = "nickname", length = 100)
    private String nickname;

    @Column(name = "phone", length = 20)
    private String phone;

    @Column(name = "email", length = 100)
    private String email;

    @Column(name = "avatar")
    private String avatar;

    @Column(name = "gender", length = 10)
    private String gender;

    @Column(name = "birthday")
    private String birthday;

    @Column(name = "address")
    private String address;

    @Column(name = "balance", precision = 10, scale = 2)
    private BigDecimal balance = BigDecimal.ZERO;

    @Column(name = "level", length = 20)
    private String level = "普通用户";

    @Column(name = "invite_code", unique = true, length = 20)
    private String inviteCode;

    @Column(name = "inviter_id")
    private Long inviterId;

    @Column(name = "status", length = 20)
    private String status = "ACTIVE"; // ACTIVE, INACTIVE, BANNED

    @Column(name = "last_login_time")
    private String lastLoginTime;

    @Column(name = "login_count")
    private Integer loginCount = 0;
}
