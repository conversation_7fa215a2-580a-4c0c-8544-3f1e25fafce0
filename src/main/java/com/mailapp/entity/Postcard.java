package com.mailapp.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 明信片实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "postcards")
public class Postcard extends BaseEntity {

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "template_id", nullable = false)
    private Long templateId;

    @Column(name = "front_text", length = 500)
    private String frontText;

    @Column(name = "back_text", columnDefinition = "TEXT")
    private String backText;

    @Column(name = "receiver_name", nullable = false, length = 100)
    private String receiverName;

    @Column(name = "receiver_phone", length = 20)
    private String receiverPhone;

    @Column(name = "receiver_address", columnDefinition = "TEXT", nullable = false)
    private String receiverAddress;

    @Column(name = "price", precision = 10, scale = 2)
    private BigDecimal price = new BigDecimal("12.00");

    @Column(name = "status", length = 20)
    private String status = "PENDING"; // PENDING, PAID, PROCESSING, SHIPPED, DELIVERED, CANCELLED

    @Column(name = "tracking_number", length = 50)
    private String trackingNumber;

    @Column(name = "payment_method", length = 20)
    private String paymentMethod;

    @Column(name = "payment_time")
    private String paymentTime;

    @Column(name = "shipped_time")
    private String shippedTime;

    @Column(name = "delivered_time")
    private String deliveredTime;

    @Column(name = "remark")
    private String remark;
}
