package com.mailapp.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 订单实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "orders")
public class Order extends BaseEntity {

    @Column(name = "order_no", unique = true, nullable = false, length = 50)
    private String orderNo;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "type", length = 20)
    private String type; // LETTER, POSTCARD, PRODUCT

    @Column(name = "total_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal totalAmount;

    @Column(name = "discount_amount", precision = 10, scale = 2)
    private BigDecimal discountAmount = BigDecimal.ZERO;

    @Column(name = "final_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal finalAmount;

    @Column(name = "status", length = 20)
    private String status = "PENDING"; // PENDING, PAID, PROCESSING, SHIPPED, DELIVERED, CANCELLED, REFUNDED

    @Column(name = "payment_method", length = 20)
    private String paymentMethod;

    @Column(name = "payment_time")
    private String paymentTime;

    @Column(name = "payment_transaction_id", length = 100)
    private String paymentTransactionId;

    @Column(name = "shipping_address", columnDefinition = "TEXT")
    private String shippingAddress;

    @Column(name = "tracking_number", length = 50)
    private String trackingNumber;

    @Column(name = "shipped_time")
    private String shippedTime;

    @Column(name = "delivered_time")
    private String deliveredTime;

    @Column(name = "remark")
    private String remark;

    @Column(name = "coupon_id")
    private Long couponId;
}
