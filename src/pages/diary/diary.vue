<template>
  <view class="diary-page">
    <view class="container">
      <!-- 日记列表 -->
      <view class="diary-list">
        <view v-for="(diary, index) in diaryList" :key="index" class="diary-item" @click="viewDiary(diary)">
          <view class="diary-date">{{ diary.date }}</view>
          <view class="diary-title">{{ diary.title }}</view>
          <view class="diary-preview">{{ diary.content }}</view>
          <view class="diary-mood">
            <text class="mood-icon">{{ diary.mood }}</text>
            <text class="mood-text">{{ diary.moodText }}</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="diaryList.length === 0" class="empty-state">
        <image src="/static/images/empty-diary.png" mode="aspectFit" />
        <text class="empty-text">还没有心语日记</text>
        <text class="empty-desc">记录生活中的美好时光</text>
      </view>

      <!-- 添加按钮 -->
      <view class="add-btn" @click="addDiary">
        <text>+</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 日记列表
const diaryList = ref([
  {
    id: 1,
    date: '2024-01-15',
    title: '美好的一天',
    content: '今天天气很好，心情也很不错。和朋友一起去了公园，看到了很多美丽的花朵...',
    mood: '😊',
    moodText: '开心'
  },
  {
    id: 2,
    date: '2024-01-14',
    title: '工作感悟',
    content: '今天在工作中学到了很多新知识，虽然有些挑战，但是收获满满...',
    mood: '💪',
    moodText: '充实'
  }
])

// 查看日记详情
const viewDiary = (diary: any) => {
  uni.navigateTo({
    url: `/pages/diary/detail?id=${diary.id}`
  })
}

// 添加新日记
const addDiary = () => {
  uni.navigateTo({
    url: '/pages/diary/edit'
  })
}
</script>

<style scoped>
.diary-page {
  background: #f8f8f8;
  min-height: 100vh;
  position: relative;
}

.container {
  padding: 20rpx;
  padding-bottom: 120rpx;
}

.diary-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.diary-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.diary-date {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.diary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.diary-preview {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.diary-mood {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.mood-icon {
  font-size: 32rpx;
}

.mood-text {
  font-size: 24rpx;
  color: #4A90E2;
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
}

.add-btn {
  position: fixed;
  right: 30rpx;
  bottom: 150rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
  z-index: 999;
}

.add-btn text {
  color: #fff;
  font-size: 48rpx;
  font-weight: 300;
}
</style>
