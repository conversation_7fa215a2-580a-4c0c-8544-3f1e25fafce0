<template>
  <view class="letter-page">
    <view class="container">
      <!-- 寄信表单 -->
      <view class="letter-form">
        <view class="form-section">
          <view class="section-title">收件人信息</view>
          <view class="form-item">
            <text class="label">收件人姓名</text>
            <input v-model="letterForm.receiverName" placeholder="请输入收件人姓名" class="input" />
          </view>
          <view class="form-item">
            <text class="label">收件人地址</text>
            <textarea v-model="letterForm.receiverAddress" placeholder="请输入详细地址" class="textarea" />
          </view>
          <view class="form-item">
            <text class="label">收件人电话</text>
            <input v-model="letterForm.receiverPhone" placeholder="请输入收件人电话" class="input" type="number" />
          </view>
        </view>

        <view class="form-section">
          <view class="section-title">寄件人信息</view>
          <view class="form-item">
            <text class="label">寄件人姓名</text>
            <input v-model="letterForm.senderName" placeholder="请输入寄件人姓名" class="input" />
          </view>
          <view class="form-item">
            <text class="label">寄件人地址</text>
            <textarea v-model="letterForm.senderAddress" placeholder="请输入详细地址" class="textarea" />
          </view>
          <view class="form-item">
            <text class="label">寄件人电话</text>
            <input v-model="letterForm.senderPhone" placeholder="请输入寄件人电话" class="input" type="number" />
          </view>
        </view>

        <view class="form-section">
          <view class="section-title">信件内容</view>
          <view class="form-item">
            <text class="label">信件标题</text>
            <input v-model="letterForm.title" placeholder="请输入信件标题" class="input" />
          </view>
          <view class="form-item">
            <text class="label">信件内容</text>
            <textarea v-model="letterForm.content" placeholder="请输入信件内容..." class="textarea content-textarea" />
          </view>
        </view>

        <view class="form-section">
          <view class="section-title">配送选项</view>
          <view class="delivery-options">
            <view 
              class="option-item" 
              :class="{ active: letterForm.deliveryType === 'standard' }"
              @click="selectDeliveryType('standard')"
            >
              <view class="option-info">
                <text class="option-title">标准配送</text>
                <text class="option-desc">3-5个工作日</text>
              </view>
              <text class="option-price">¥8</text>
            </view>
            <view 
              class="option-item" 
              :class="{ active: letterForm.deliveryType === 'express' }"
              @click="selectDeliveryType('express')"
            >
              <view class="option-info">
                <text class="option-title">加急配送</text>
                <text class="option-desc">1-2个工作日</text>
              </view>
              <text class="option-price">¥15</text>
            </view>
          </view>
        </view>

        <!-- 提交按钮 -->
        <button class="submit-btn" @click="submitLetter">确认寄出</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// 表单数据
const letterForm = reactive({
  receiverName: '',
  receiverAddress: '',
  receiverPhone: '',
  senderName: '',
  senderAddress: '',
  senderPhone: '',
  title: '',
  content: '',
  deliveryType: 'standard'
})

// 选择配送方式
const selectDeliveryType = (type: string) => {
  letterForm.deliveryType = type
}

// 提交信件
const submitLetter = () => {
  // 表单验证
  if (!letterForm.receiverName) {
    uni.showToast({ title: '请输入收件人姓名', icon: 'none' })
    return
  }
  if (!letterForm.receiverAddress) {
    uni.showToast({ title: '请输入收件人地址', icon: 'none' })
    return
  }
  if (!letterForm.senderName) {
    uni.showToast({ title: '请输入寄件人姓名', icon: 'none' })
    return
  }
  if (!letterForm.content) {
    uni.showToast({ title: '请输入信件内容', icon: 'none' })
    return
  }

  // 显示确认弹窗
  uni.showModal({
    title: '确认寄出',
    content: `配送方式：${letterForm.deliveryType === 'standard' ? '标准配送 ¥8' : '加急配送 ¥15'}`,
    success: (res) => {
      if (res.confirm) {
        // 这里应该调用API提交数据
        uni.showToast({ title: '寄出成功！', icon: 'success' })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    }
  })
}
</script>

<style scoped>
.letter-page {
  background: #f8f8f8;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

.letter-form {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.form-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.textarea {
  width: 100%;
  min-height: 120rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.content-textarea {
  min-height: 200rpx;
}

.delivery-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #fafafa;
}

.option-item.active {
  border-color: #4A90E2;
  background: #f0f7ff;
}

.option-info {
  flex: 1;
}

.option-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #999;
}

.option-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #4A90E2;
}

.submit-btn {
  margin: 40rpx 30rpx 30rpx;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  height: 88rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.submit-btn:active {
  opacity: 0.8;
}
</style>
