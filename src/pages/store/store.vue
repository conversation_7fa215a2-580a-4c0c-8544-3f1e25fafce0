<template>
  <view class="store-page">
    <view class="container">
      <!-- 搜索栏 -->
      <view class="search-bar">
        <view class="search-box">
          <image src="/static/icons/search.png" class="search-icon" />
          <input v-model="searchKeyword" placeholder="搜索商品" class="search-input" />
        </view>
      </view>

      <!-- 分类标签 -->
      <view class="category-tabs">
        <scroll-view class="category-scroll" scroll-x>
          <view class="category-list">
            <view 
              v-for="(category, index) in categories" 
              :key="index"
              class="category-item"
              :class="{ active: activeCategory === index }"
              @click="switchCategory(index)"
            >
              {{ category.name }}
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 商品列表 -->
      <view class="product-grid">
        <view 
          v-for="(product, index) in filteredProducts" 
          :key="index"
          class="product-item"
          @click="viewProduct(product)"
        >
          <view class="product-image">
            <image :src="product.image" mode="aspectFill" />
            <view v-if="product.discount" class="discount-badge">{{ product.discount }}折</view>
          </view>
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <text class="product-desc">{{ product.desc }}</text>
            <view class="product-price">
              <text class="current-price">¥{{ product.price }}</text>
              <text v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</text>
            </view>
            <view class="product-sales">已售{{ product.sales }}件</view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="filteredProducts.length === 0" class="empty-state">
        <image src="/static/images/empty-store.png" mode="aspectFit" />
        <text class="empty-text">暂无商品</text>
        <text class="empty-desc">敬请期待更多精彩商品</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 搜索关键词
const searchKeyword = ref('')

// 分类列表
const categories = ref([
  { name: '全部', id: 'all' },
  { name: '信纸', id: 'paper' },
  { name: '信封', id: 'envelope' },
  { name: '明信片', id: 'postcard' },
  { name: '邮票', id: 'stamp' },
  { name: '文具', id: 'stationery' }
])

// 当前选中的分类
const activeCategory = ref(0)

// 商品列表
const productList = ref([
  {
    id: 1,
    name: '4.2回信信封3个(可查物流)',
    desc: '精美信封，支持物流查询',
    price: 18.8,
    originalPrice: null,
    discount: null,
    image: '/static/images/envelope1.jpg',
    category: 'envelope',
    sales: 128
  },
  {
    id: 2,
    name: '【信封】3个',
    desc: '经典款信封，质量保证',
    price: 8.0,
    originalPrice: null,
    discount: null,
    image: '/static/images/envelope2.jpg',
    category: 'envelope',
    sales: 89
  },
  {
    id: 3,
    name: '2026年日历卡',
    desc: '精美日历设计，实用美观',
    price: 2.5,
    originalPrice: null,
    discount: null,
    image: '/static/images/calendar.jpg',
    category: 'stationery',
    sales: 256
  },
  {
    id: 4,
    name: '未来可期信纸(5张)',
    desc: '励志主题信纸，传递正能量',
    price: 5.0,
    originalPrice: null,
    discount: null,
    image: '/static/images/paper1.jpg',
    category: 'paper',
    sales: 45
  },
  {
    id: 5,
    name: '彩色信纸套装',
    desc: '多种颜色，丰富选择',
    price: 12.0,
    originalPrice: 15.0,
    discount: 8,
    image: '/static/images/paper2.jpg',
    category: 'paper',
    sales: 167
  },
  {
    id: 6,
    name: '复古明信片套装',
    desc: '怀旧风格，收藏佳品',
    price: 25.0,
    originalPrice: null,
    discount: null,
    image: '/static/images/postcard_set.jpg',
    category: 'postcard',
    sales: 78
  }
])

// 过滤后的商品列表
const filteredProducts = computed(() => {
  let products = productList.value

  // 按分类过滤
  const categoryId = categories.value[activeCategory.value].id
  if (categoryId !== 'all') {
    products = products.filter(product => product.category === categoryId)
  }

  // 按搜索关键词过滤
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.trim().toLowerCase()
    products = products.filter(product => 
      product.name.toLowerCase().includes(keyword) ||
      product.desc.toLowerCase().includes(keyword)
    )
  }

  return products
})

// 切换分类
const switchCategory = (index: number) => {
  activeCategory.value = index
}

// 查看商品详情
const viewProduct = (product: any) => {
  uni.navigateTo({
    url: `/pages/store/detail?id=${product.id}`
  })
}
</script>

<style scoped>
.store-page {
  background: #f8f8f8;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

/* 搜索栏 */
.search-bar {
  margin-bottom: 20rpx;
}

.search-box {
  background: #fff;
  border-radius: 50rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  height: 80rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 分类标签 */
.category-tabs {
  margin-bottom: 20rpx;
}

.category-scroll {
  width: 100%;
}

.category-list {
  display: flex;
  gap: 20rpx;
  padding-bottom: 10rpx;
}

.category-item {
  flex-shrink: 0;
  padding: 16rpx 32rpx;
  background: #fff;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #666;
  border: 2rpx solid transparent;
}

.category-item.active {
  background: #4A90E2;
  color: #fff;
}

/* 商品网格 */
.product-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.product-item {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.product-image {
  position: relative;
  height: 300rpx;
}

.product-image image {
  width: 100%;
  height: 100%;
}

.discount-badge {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background: #ff4d4f;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 8rpx;
}

.current-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4d4f;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.product-sales {
  font-size: 22rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
}
</style>
