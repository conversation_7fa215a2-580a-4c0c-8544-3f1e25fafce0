<template>
  <view class="index-page">
    <!-- 轮播图 -->
    <swiper class="banner-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
      <swiper-item v-for="(item, index) in bannerList" :key="index">
        <view class="banner-item" :style="{ backgroundImage: `url(${item.image})` }">
          <view class="banner-content">
            <view class="banner-title">{{ item.title }}</view>
            <view class="banner-subtitle">{{ item.subtitle }}</view>
            <button class="banner-btn" @click="handleBannerClick(item)">{{ item.buttonText }}</button>
          </view>
        </view>
      </swiper-item>
    </swiper>

    <!-- 主要功能区 -->
    <view class="main-functions">
      <view class="function-card" @click="navigateTo('/pages/letter/letter')">
        <view class="function-icon">
          <image src="/static/icons/letter.png" mode="aspectFit" />
        </view>
        <view class="function-content">
          <view class="function-title">寄信</view>
          <view class="function-subtitle">ORDINARYLEYYER</view>
          <view class="function-desc">一封书信，温暖人心</view>
        </view>
        <view class="function-arrow">></view>
      </view>

      <view class="function-card" @click="navigateTo('/pages/postcard/postcard')">
        <view class="function-icon">
          <image src="/static/icons/postcard.png" mode="aspectFit" />
        </view>
        <view class="function-content">
          <view class="function-title">寄明信片</view>
          <view class="function-subtitle">POSTCARD</view>
          <view class="function-desc">一张明信片，纸短情长</view>
        </view>
        <view class="function-arrow">></view>
      </view>
    </view>

    <!-- 其他功能区 -->
    <view class="other-functions">
      <view class="function-grid">
        <view class="grid-item" @click="navigateTo('/pages/diary/diary')">
          <view class="grid-icon">
            <image src="/static/icons/diary.png" mode="aspectFit" />
          </view>
          <view class="grid-title">心语日记</view>
          <view class="grid-desc">心情随笔写入青春</view>
        </view>

        <view class="grid-item" @click="navigateTo('/pages/logistics/logistics')">
          <view class="grid-icon">
            <image src="/static/icons/logistics.png" mode="aspectFit" />
          </view>
          <view class="grid-title">回信物流</view>
          <view class="grid-desc">物流信息可查询</view>
        </view>

        <view class="grid-item" @click="navigateTo('/pages/countdown/countdown')">
          <view class="grid-icon">
            <image src="/static/icons/countdown.png" mode="aspectFit" />
          </view>
          <view class="grid-title">回家倒计时</view>
          <view class="grid-desc">时刻关注归家之时</view>
        </view>

        <view class="grid-item" @click="navigateTo('/pages/mailbox/mailbox')">
          <view class="grid-icon">
            <image src="/static/icons/mailbox.png" mode="aspectFit" />
          </view>
          <view class="grid-title">代收信箱</view>
          <view class="grid-desc">暂时代收线上收信</view>
        </view>
      </view>
    </view>

    <!-- 客服按钮 -->
    <view class="customer-service" @click="contactService">
      <image src="/static/icons/service.png" mode="aspectFit" />
      <text>联系客服</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 轮播图数据
const bannerList = ref([
  {
    image: '/static/images/banner1.jpg',
    title: '线上寄信',
    subtitle: '网络时代，就是便捷',
    buttonText: '点我出发属群',
    action: 'letter'
  }
])

// 页面跳转
const navigateTo = (url: string) => {
  uni.navigateTo({ url })
}

// 轮播图点击
const handleBannerClick = (item: any) => {
  if (item.action === 'letter') {
    navigateTo('/pages/letter/letter')
  }
}

// 联系客服
const contactService = () => {
  uni.showModal({
    title: '联系客服',
    content: '请拨打客服电话：400-123-4567',
    showCancel: false
  })
}
</script>

<style scoped>
.index-page {
  background: #f8f8f8;
  min-height: 100vh;
}

/* 轮播图样式 */
.banner-swiper {
  height: 400rpx;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.banner-item {
  height: 100%;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 40rpx;
}

.banner-content {
  color: #fff;
}

.banner-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.banner-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 30rpx;
}

.banner-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid #fff;
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

/* 主要功能区 */
.main-functions {
  margin: 20rpx;
}

.function-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.function-icon {
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
}

.function-icon image {
  width: 100%;
  height: 100%;
}

.function-content {
  flex: 1;
}

.function-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.function-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.function-desc {
  font-size: 28rpx;
  color: #666;
}

.function-arrow {
  font-size: 32rpx;
  color: #ccc;
}

/* 其他功能区 */
.other-functions {
  margin: 20rpx;
}

.function-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.grid-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.grid-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 20rpx;
}

.grid-icon image {
  width: 100%;
  height: 100%;
}

.grid-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.grid-desc {
  font-size: 24rpx;
  color: #999;
}

/* 客服按钮 */
.customer-service {
  position: fixed;
  right: 30rpx;
  bottom: 150rpx;
  background: #ff6b35;
  color: #fff;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
  z-index: 999;
}

.customer-service image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.customer-service text {
  font-size: 24rpx;
}
</style>
