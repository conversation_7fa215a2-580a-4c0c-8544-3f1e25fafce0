<template>
  <view class="gift-page">
    <!-- 主要邀请区域 -->
    <view class="main-invite-section">
      <view class="invite-bg">
        <view class="invite-content">
          <view class="invite-title">邀好友 领现金</view>
          <view class="invite-subtitle">邀请新用户注册<br/>双方互得5元优惠券</view>
        </view>
        <view class="invite-illustration">
          <!-- 这里可以放置插画图片 -->
          <view class="illustration-placeholder">🎁</view>
        </view>
      </view>
    </view>

    <!-- 第一重好礼 -->
    <view class="gift-card">
      <view class="gift-header">
        <view class="gift-icon">🎁</view>
        <view class="gift-info">
          <view class="gift-badge">第一重好礼</view>
          <view class="gift-desc">邀请新用户注册<br/>双方互得5元优惠券</view>
        </view>
      </view>
      <button class="invite-btn" @click="inviteNow">立即邀请</button>
    </view>

    <!-- 第二重好礼 -->
    <view class="gift-card">
      <view class="gift-header">
        <view class="gift-icon">💰</view>
        <view class="gift-info">
          <view class="gift-badge">第二重好礼</view>
          <view class="gift-desc">好友首次消费满20元<br/>您可获得3元现金奖励</view>
        </view>
      </view>
      <button class="invite-btn secondary" @click="inviteNow">立即邀请</button>
    </view>

    <!-- 邀请码和分享 -->
    <view class="invite-code-section">
      <view class="section-title">我的邀请码</view>
      <view class="invite-code-box">
        <text class="invite-code">{{ inviteCode }}</text>
        <button class="copy-btn" @click="copyInviteCode">复制</button>
      </view>
      <text class="invite-tip">分享邀请码给好友，好友注册成功后您将获得奖励</text>

      <!-- 分享按钮 -->
      <view class="share-buttons">
        <button class="share-btn wechat" @click="shareToWeChat">
          <text class="share-icon">💬</text>
          <text>微信好友</text>
        </button>
        <button class="share-btn moments" @click="shareToMoments">
          <text class="share-icon">📱</text>
          <text>朋友圈</text>
        </button>
      </view>
    </view>

    <!-- 邀请记录 -->
    <view class="invite-records">
      <view class="section-title">我的邀请记录</view>
      <view v-if="inviteList.length > 0" class="records-list">
        <view v-for="(record, index) in inviteList" :key="index" class="record-item">
          <view class="record-avatar">
            <text>{{ record.nickname.charAt(0) }}</text>
          </view>
          <view class="record-info">
            <text class="record-name">{{ record.nickname }}</text>
            <text class="record-time">{{ record.time }}</text>
          </view>
          <view class="record-status" :class="record.status">
            {{ getStatusText(record.status) }}
          </view>
        </view>
      </view>
      <view v-else class="empty-records">
        <text>暂无邀请记录</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 邀请码
const inviteCode = ref('MAIL2024ABC')

// 立即邀请
const inviteNow = () => {
  shareToWeChat()
}

// 邀请记录
const inviteList = ref([
  {
    nickname: '张小明',
    time: '2024-01-15 14:30',
    status: 'success'
  },
  {
    nickname: '李小红',
    time: '2024-01-14 10:20',
    status: 'success'
  },
  {
    nickname: '王大华',
    time: '2024-01-13 16:45',
    status: 'pending'
  },
  {
    nickname: '陈小美',
    time: '2024-01-12 09:15',
    status: 'success'
  },
  {
    nickname: '刘小强',
    time: '2024-01-11 20:30',
    status: 'success'
  }
])

// 复制邀请码
const copyInviteCode = () => {
  uni.setClipboardData({
    data: inviteCode.value,
    success: () => {
      uni.showToast({ title: '邀请码已复制', icon: 'success' })
    }
  })
}

// 分享到微信好友
const shareToWeChat = () => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: `https://mailapp.com/invite?code=${inviteCode.value}`,
    title: '信件助理邀请',
    summary: '线上寄信，就是便捷！快来体验吧！',
    imageUrl: '/static/images/share-logo.png',
    success: () => {
      uni.showToast({ title: '分享成功', icon: 'success' })
    },
    fail: () => {
      uni.showToast({ title: '分享失败', icon: 'none' })
    }
  })
}

// 分享到朋友圈
const shareToMoments = () => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneTimeline',
    type: 0,
    href: `https://mailapp.com/invite?code=${inviteCode.value}`,
    title: '信件助理邀请 - 线上寄信，就是便捷！',
    imageUrl: '/static/images/share-logo.png',
    success: () => {
      uni.showToast({ title: '分享成功', icon: 'success' })
    },
    fail: () => {
      uni.showToast({ title: '分享失败', icon: 'none' })
    }
  })
}

// 分享到QQ好友
const shareToQQ = () => {
  uni.showToast({ title: 'QQ分享功能开发中', icon: 'none' })
}

// 分享到QQ空间
const shareToQzone = () => {
  uni.showToast({ title: 'QQ空间分享功能开发中', icon: 'none' })
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    'success': '已注册',
    'pending': '待注册',
    'expired': '已过期'
  }
  return statusMap[status] || '未知'
}
</script>

<style scoped>
.gift-page {
  background: #f8f8f8;
  min-height: 100vh;
}

/* 主要邀请区域 */
.main-invite-section {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  padding: 60rpx 40rpx;
  position: relative;
  overflow: hidden;
}

.invite-bg {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #fff;
}

.invite-content {
  flex: 1;
}

.invite-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.invite-subtitle {
  font-size: 28rpx;
  line-height: 1.5;
  opacity: 0.9;
}

.invite-illustration {
  width: 200rpx;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.illustration-placeholder {
  font-size: 120rpx;
  opacity: 0.8;
}

/* 礼品卡片 */
.gift-card {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.gift-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.gift-icon {
  font-size: 60rpx;
}

.gift-info {
  flex: 1;
}

.gift-badge {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  display: inline-block;
  margin-bottom: 12rpx;
}

.gift-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.invite-btn {
  background: linear-gradient(135deg, #ffd93d 0%, #ffcd02 100%);
  color: #333;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(255, 217, 61, 0.3);
}

.invite-btn.secondary {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

/* 邀请码 */
.invite-code-section {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.invite-code-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.invite-code {
  flex: 1;
  background: #f0f7ff;
  color: #4A90E2;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  border: 2rpx dashed #4A90E2;
}

.copy-btn {
  background: #4A90E2;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
}

.invite-tip {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 30rpx;
}

/* 分享按钮 */
.share-buttons {
  display: flex;
  gap: 20rpx;
}

.share-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 30rpx 20rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
}

.share-btn.wechat {
  background: linear-gradient(135deg, #07c160 0%, #00ae42 100%);
  color: #fff;
}

.share-btn.moments {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #fff;
}

.share-icon {
  font-size: 40rpx;
}

/* 分享方式 */
.share-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.share-methods {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
}

.share-item {
  text-align: center;
}

.share-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
}

.share-text {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 奖励规则 */
.reward-rules {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.rules-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.rule-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #fafafa;
  border-radius: 12rpx;
}

.rule-icon {
  font-size: 40rpx;
  width: 60rpx;
  text-align: center;
}

.rule-content {
  flex: 1;
}

.rule-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.rule-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 邀请记录 */
.invite-records {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.record-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #fafafa;
  border-radius: 12rpx;
}

.record-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #4A90E2;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.record-info {
  flex: 1;
}

.record-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #fff;
}

.record-status.success {
  background: #52c41a;
}

.record-status.pending {
  background: #faad14;
}

.record-status.expired {
  background: #ff4d4f;
}

.empty-records {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}
</style>
