<template>
  <view class="gift-page">
    <view class="container">
      <!-- 邀请统计 -->
      <view class="invite-stats">
        <view class="stats-header">
          <text class="stats-title">我的邀请</text>
          <text class="stats-desc">邀请好友，共享便捷寄信服务</text>
        </view>
        <view class="stats-content">
          <view class="stat-item">
            <text class="stat-number">{{ inviteStats.total }}</text>
            <text class="stat-label">累计邀请</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ inviteStats.success }}</text>
            <text class="stat-label">成功注册</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ inviteStats.reward }}</text>
            <text class="stat-label">获得奖励</text>
          </view>
        </view>
      </view>

      <!-- 邀请码 -->
      <view class="invite-code-section">
        <view class="section-title">我的邀请码</view>
        <view class="invite-code-box">
          <text class="invite-code">{{ inviteCode }}</text>
          <button class="copy-btn" @click="copyInviteCode">复制</button>
        </view>
        <text class="invite-tip">分享邀请码给好友，好友注册成功后您将获得奖励</text>
      </view>

      <!-- 分享方式 -->
      <view class="share-section">
        <view class="section-title">分享给好友</view>
        <view class="share-methods">
          <view class="share-item" @click="shareToWeChat">
            <image src="/static/icons/wechat.png" class="share-icon" />
            <text class="share-text">微信好友</text>
          </view>
          <view class="share-item" @click="shareToMoments">
            <image src="/static/icons/moments.png" class="share-icon" />
            <text class="share-text">朋友圈</text>
          </view>
          <view class="share-item" @click="shareToQQ">
            <image src="/static/icons/qq.png" class="share-icon" />
            <text class="share-text">QQ好友</text>
          </view>
          <view class="share-item" @click="shareToQzone">
            <image src="/static/icons/qzone.png" class="share-icon" />
            <text class="share-text">QQ空间</text>
          </view>
        </view>
      </view>

      <!-- 奖励规则 -->
      <view class="reward-rules">
        <view class="section-title">奖励规则</view>
        <view class="rules-list">
          <view class="rule-item">
            <view class="rule-icon">🎁</view>
            <view class="rule-content">
              <text class="rule-title">邀请奖励</text>
              <text class="rule-desc">好友成功注册，您可获得5元优惠券</text>
            </view>
          </view>
          <view class="rule-item">
            <view class="rule-icon">💰</view>
            <view class="rule-content">
              <text class="rule-title">消费奖励</text>
              <text class="rule-desc">好友首次消费，您可获得3元现金奖励</text>
            </view>
          </view>
          <view class="rule-item">
            <view class="rule-icon">🏆</view>
            <view class="rule-content">
              <text class="rule-title">等级奖励</text>
              <text class="rule-desc">邀请满10人，升级为VIP用户</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 邀请记录 -->
      <view class="invite-records">
        <view class="section-title">邀请记录</view>
        <view v-if="inviteList.length > 0" class="records-list">
          <view v-for="(record, index) in inviteList" :key="index" class="record-item">
            <view class="record-avatar">
              <text>{{ record.nickname.charAt(0) }}</text>
            </view>
            <view class="record-info">
              <text class="record-name">{{ record.nickname }}</text>
              <text class="record-time">{{ record.time }}</text>
            </view>
            <view class="record-status" :class="record.status">
              {{ getStatusText(record.status) }}
            </view>
          </view>
        </view>
        <view v-else class="empty-records">
          <text>暂无邀请记录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 邀请统计
const inviteStats = ref({
  total: 8,
  success: 5,
  reward: 25
})

// 邀请码
const inviteCode = ref('MAIL2024ABC')

// 邀请记录
const inviteList = ref([
  {
    nickname: '张小明',
    time: '2024-01-15 14:30',
    status: 'success'
  },
  {
    nickname: '李小红',
    time: '2024-01-14 10:20',
    status: 'success'
  },
  {
    nickname: '王大华',
    time: '2024-01-13 16:45',
    status: 'pending'
  },
  {
    nickname: '陈小美',
    time: '2024-01-12 09:15',
    status: 'success'
  },
  {
    nickname: '刘小强',
    time: '2024-01-11 20:30',
    status: 'success'
  }
])

// 复制邀请码
const copyInviteCode = () => {
  uni.setClipboardData({
    data: inviteCode.value,
    success: () => {
      uni.showToast({ title: '邀请码已复制', icon: 'success' })
    }
  })
}

// 分享到微信好友
const shareToWeChat = () => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: `https://mailapp.com/invite?code=${inviteCode.value}`,
    title: '信件助理邀请',
    summary: '线上寄信，就是便捷！快来体验吧！',
    imageUrl: '/static/images/share-logo.png',
    success: () => {
      uni.showToast({ title: '分享成功', icon: 'success' })
    },
    fail: () => {
      uni.showToast({ title: '分享失败', icon: 'none' })
    }
  })
}

// 分享到朋友圈
const shareToMoments = () => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneTimeline',
    type: 0,
    href: `https://mailapp.com/invite?code=${inviteCode.value}`,
    title: '信件助理邀请 - 线上寄信，就是便捷！',
    imageUrl: '/static/images/share-logo.png',
    success: () => {
      uni.showToast({ title: '分享成功', icon: 'success' })
    },
    fail: () => {
      uni.showToast({ title: '分享失败', icon: 'none' })
    }
  })
}

// 分享到QQ好友
const shareToQQ = () => {
  uni.showToast({ title: 'QQ分享功能开发中', icon: 'none' })
}

// 分享到QQ空间
const shareToQzone = () => {
  uni.showToast({ title: 'QQ空间分享功能开发中', icon: 'none' })
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    'success': '已注册',
    'pending': '待注册',
    'expired': '已过期'
  }
  return statusMap[status] || '未知'
}
</script>

<style scoped>
.gift-page {
  background: #f8f8f8;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

/* 邀请统计 */
.invite-stats {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  color: #fff;
}

.stats-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.stats-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stats-desc {
  font-size: 24rpx;
  opacity: 0.8;
}

.stats-content {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 邀请码 */
.invite-code-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.invite-code-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.invite-code {
  flex: 1;
  background: #f0f7ff;
  color: #4A90E2;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  border: 2rpx dashed #4A90E2;
}

.copy-btn {
  background: #4A90E2;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
}

.invite-tip {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

/* 分享方式 */
.share-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.share-methods {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
}

.share-item {
  text-align: center;
}

.share-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
}

.share-text {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 奖励规则 */
.reward-rules {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.rules-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.rule-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #fafafa;
  border-radius: 12rpx;
}

.rule-icon {
  font-size: 40rpx;
  width: 60rpx;
  text-align: center;
}

.rule-content {
  flex: 1;
}

.rule-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.rule-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 邀请记录 */
.invite-records {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.record-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #fafafa;
  border-radius: 12rpx;
}

.record-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #4A90E2;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.record-info {
  flex: 1;
}

.record-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #fff;
}

.record-status.success {
  background: #52c41a;
}

.record-status.pending {
  background: #faad14;
}

.record-status.expired {
  background: #ff4d4f;
}

.empty-records {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}
</style>
