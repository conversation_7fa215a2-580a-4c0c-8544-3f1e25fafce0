<template>
  <view class="mailbox-page">
    <view class="container">
      <!-- 信箱统计 -->
      <view class="mailbox-stats">
        <view class="stat-item">
          <text class="stat-number">{{ mailStats.total }}</text>
          <text class="stat-label">总信件</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ mailStats.unread }}</text>
          <text class="stat-label">未读</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ mailStats.today }}</text>
          <text class="stat-label">今日新增</text>
        </view>
      </view>

      <!-- 筛选标签 -->
      <view class="filter-tabs">
        <view 
          v-for="(tab, index) in filterTabs" 
          :key="index"
          class="filter-tab"
          :class="{ active: activeTab === index }"
          @click="switchTab(index)"
        >
          {{ tab.name }}
          <text v-if="tab.count > 0" class="tab-count">{{ tab.count }}</text>
        </view>
      </view>

      <!-- 信件列表 -->
      <view class="mail-list">
        <view 
          v-for="(mail, index) in filteredMails" 
          :key="index"
          class="mail-item"
          :class="{ unread: !mail.isRead }"
          @click="openMail(mail)"
        >
          <view class="mail-avatar">
            <text>{{ mail.senderName.charAt(0) }}</text>
          </view>
          <view class="mail-content">
            <view class="mail-header">
              <text class="sender-name">{{ mail.senderName }}</text>
              <text class="mail-time">{{ formatTime(mail.time) }}</text>
            </view>
            <view class="mail-title">{{ mail.title }}</view>
            <view class="mail-preview">{{ mail.preview }}</view>
            <view class="mail-tags">
              <text v-if="mail.type === 'letter'" class="mail-tag letter">信件</text>
              <text v-if="mail.type === 'postcard'" class="mail-tag postcard">明信片</text>
              <text v-if="mail.urgent" class="mail-tag urgent">加急</text>
            </view>
          </view>
          <view v-if="!mail.isRead" class="unread-dot"></view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="filteredMails.length === 0" class="empty-state">
        <image src="/static/images/empty-mailbox.png" mode="aspectFit" />
        <text class="empty-text">信箱空空如也</text>
        <text class="empty-desc">等待第一封信件的到来</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 信箱统计
const mailStats = ref({
  total: 15,
  unread: 3,
  today: 2
})

// 筛选标签
const filterTabs = ref([
  { name: '全部', count: 0, filter: 'all' },
  { name: '未读', count: 3, filter: 'unread' },
  { name: '信件', count: 10, filter: 'letter' },
  { name: '明信片', count: 5, filter: 'postcard' }
])

// 当前选中的标签
const activeTab = ref(0)

// 信件列表
const mailList = ref([
  {
    id: 1,
    senderName: '张小明',
    title: '给远方朋友的一封信',
    preview: '好久不见，最近过得怎么样？我这边一切都好...',
    time: '2024-01-15 14:30',
    type: 'letter',
    isRead: false,
    urgent: false
  },
  {
    id: 2,
    senderName: '李小红',
    title: '生日祝福',
    preview: '祝你生日快乐！愿你的每一天都充满阳光...',
    time: '2024-01-15 10:20',
    type: 'postcard',
    isRead: false,
    urgent: true
  },
  {
    id: 3,
    senderName: '王大华',
    title: '工作汇报',
    preview: '本月的工作总结如下，请查收...',
    time: '2024-01-14 16:45',
    type: 'letter',
    isRead: true,
    urgent: false
  },
  {
    id: 4,
    senderName: '陈小美',
    title: '旅行明信片',
    preview: '这里的风景真美，分享给你看看...',
    time: '2024-01-14 09:15',
    type: 'postcard',
    isRead: true,
    urgent: false
  },
  {
    id: 5,
    senderName: '刘小强',
    title: '感谢信',
    preview: '非常感谢你的帮助，让我度过了难关...',
    time: '2024-01-13 20:30',
    type: 'letter',
    isRead: false,
    urgent: false
  }
])

// 过滤后的信件列表
const filteredMails = computed(() => {
  const filter = filterTabs.value[activeTab.value].filter
  
  switch (filter) {
    case 'unread':
      return mailList.value.filter(mail => !mail.isRead)
    case 'letter':
      return mailList.value.filter(mail => mail.type === 'letter')
    case 'postcard':
      return mailList.value.filter(mail => mail.type === 'postcard')
    default:
      return mailList.value
  }
})

// 切换标签
const switchTab = (index: number) => {
  activeTab.value = index
}

// 打开信件
const openMail = (mail: any) => {
  // 标记为已读
  if (!mail.isRead) {
    mail.isRead = true
    mailStats.value.unread--
    // 更新未读标签的数量
    const unreadTab = filterTabs.value.find(tab => tab.filter === 'unread')
    if (unreadTab) {
      unreadTab.count--
    }
  }

  // 跳转到信件详情页
  uni.navigateTo({
    url: `/pages/mailbox/detail?id=${mail.id}`
  })
}

// 格式化时间
const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return timeStr.split(' ')[0]
  }
}
</script>

<style scoped>
.mailbox-page {
  background: #f8f8f8;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

/* 信箱统计 */
.mailbox-stats {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-around;
  color: #fff;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  padding: 10rpx;
  margin-bottom: 20rpx;
  gap: 10rpx;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.filter-tab.active {
  background: #4A90E2;
  color: #fff;
}

.tab-count {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: #ff4d4f;
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  height: 20rpx;
  line-height: 20rpx;
}

/* 信件列表 */
.mail-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.mail-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  position: relative;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.mail-item.unread {
  border-left: 6rpx solid #4A90E2;
}

.mail-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #4A90E2;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.mail-content {
  flex: 1;
}

.mail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.sender-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.mail-time {
  font-size: 24rpx;
  color: #999;
}

.mail-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.mail-preview {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.mail-tags {
  display: flex;
  gap: 10rpx;
}

.mail-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #fff;
}

.mail-tag.letter {
  background: #52c41a;
}

.mail-tag.postcard {
  background: #faad14;
}

.mail-tag.urgent {
  background: #ff4d4f;
}

.unread-dot {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #ff4d4f;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
}
</style>
