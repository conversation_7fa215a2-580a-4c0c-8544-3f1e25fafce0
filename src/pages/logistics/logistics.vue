<template>
  <view class="logistics-page">
    <view class="container">
      <!-- 查询区域 -->
      <view class="search-section">
        <view class="search-box">
          <input v-model="trackingNumber" placeholder="请输入快递单号" class="search-input" />
          <button class="search-btn" @click="searchLogistics">查询</button>
        </view>
      </view>

      <!-- 物流信息 -->
      <view v-if="logisticsInfo" class="logistics-info">
        <view class="info-header">
          <view class="package-info">
            <text class="package-title">{{ logisticsInfo.title }}</text>
            <text class="package-number">单号：{{ logisticsInfo.number }}</text>
          </view>
          <view class="status-badge" :class="logisticsInfo.status">
            {{ getStatusText(logisticsInfo.status) }}
          </view>
        </view>

        <!-- 物流轨迹 */
        <view class="logistics-track">
          <view class="track-title">物流轨迹</view>
          <view class="track-list">
            <view 
              v-for="(track, index) in logisticsInfo.tracks" 
              :key="index"
              class="track-item"
              :class="{ active: index === 0 }"
            >
              <view class="track-dot"></view>
              <view class="track-content">
                <view class="track-desc">{{ track.desc }}</view>
                <view class="track-time">{{ track.time }}</view>
                <view v-if="track.location" class="track-location">{{ track.location }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 历史查询 -->
      <view class="history-section">
        <view class="section-title">历史查询</view>
        <view v-if="historyList.length > 0" class="history-list">
          <view 
            v-for="(item, index) in historyList" 
            :key="index"
            class="history-item"
            @click="searchHistory(item)"
          >
            <view class="history-info">
              <text class="history-title">{{ item.title }}</text>
              <text class="history-number">{{ item.number }}</text>
            </view>
            <view class="history-status" :class="item.status">
              {{ getStatusText(item.status) }}
            </view>
          </view>
        </view>
        <view v-else class="empty-history">
          <text>暂无查询历史</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 查询单号
const trackingNumber = ref('')

// 物流信息
const logisticsInfo = ref(null)

// 历史查询列表
const historyList = ref([
  {
    title: '给妈妈的信',
    number: 'SF1234567890',
    status: 'delivered'
  },
  {
    title: '明信片',
    number: 'YT9876543210',
    status: 'shipping'
  }
])

// 查询物流
const searchLogistics = () => {
  if (!trackingNumber.value.trim()) {
    uni.showToast({ title: '请输入快递单号', icon: 'none' })
    return
  }

  // 模拟查询结果
  logisticsInfo.value = {
    title: '信件包裹',
    number: trackingNumber.value,
    status: 'shipping',
    tracks: [
      {
        desc: '快件已送达，感谢使用信件助理',
        time: '2024-01-15 14:30:00',
        location: '北京市朝阳区'
      },
      {
        desc: '快件正在派送中，请保持电话畅通',
        time: '2024-01-15 09:15:00',
        location: '北京市朝阳区营业部'
      },
      {
        desc: '快件已到达目的地网点',
        time: '2024-01-15 06:20:00',
        location: '北京市朝阳区分拣中心'
      },
      {
        desc: '快件正在运输途中',
        time: '2024-01-14 20:45:00',
        location: '上海市浦东新区转运中心'
      },
      {
        desc: '快件已发出',
        time: '2024-01-14 16:30:00',
        location: '上海市黄浦区营业部'
      }
    ]
  }

  // 添加到历史记录
  const existIndex = historyList.value.findIndex(item => item.number === trackingNumber.value)
  if (existIndex === -1) {
    historyList.value.unshift({
      title: '信件包裹',
      number: trackingNumber.value,
      status: 'shipping'
    })
  }
}

// 查询历史记录
const searchHistory = (item: any) => {
  trackingNumber.value = item.number
  searchLogistics()
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    'shipping': '运输中',
    'delivered': '已送达',
    'pending': '待发货',
    'exception': '异常'
  }
  return statusMap[status] || '未知'
}
</script>

<style scoped>
.logistics-page {
  background: #f8f8f8;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

/* 搜索区域 */
.search-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.search-box {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.search-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.search-btn {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 0 30rpx;
  height: 80rpx;
  font-size: 28rpx;
}

/* 物流信息 */
.logistics-info {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.package-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.package-number {
  font-size: 24rpx;
  color: #999;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.status-badge.shipping {
  background: #4A90E2;
}

.status-badge.delivered {
  background: #52c41a;
}

.status-badge.pending {
  background: #faad14;
}

.status-badge.exception {
  background: #ff4d4f;
}

/* 物流轨迹 */
.track-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.track-list {
  position: relative;
}

.track-item {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
}

.track-item:last-child {
  margin-bottom: 0;
}

.track-item::before {
  content: '';
  position: absolute;
  left: 12rpx;
  top: 30rpx;
  bottom: -30rpx;
  width: 2rpx;
  background: #e0e0e0;
}

.track-item:last-child::before {
  display: none;
}

.track-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background: #e0e0e0;
  margin-right: 20rpx;
  margin-top: 8rpx;
  flex-shrink: 0;
}

.track-item.active .track-dot {
  background: #4A90E2;
}

.track-content {
  flex: 1;
}

.track-desc {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.track-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.track-location {
  font-size: 24rpx;
  color: #666;
}

/* 历史查询 */
.history-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #fafafa;
  border-radius: 8rpx;
}

.history-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.history-number {
  font-size: 24rpx;
  color: #999;
}

.history-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #fff;
}

.empty-history {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}
</style>
