<template>
  <view class="profile-page">
    <!-- 用户信息区域 -->
    <view class="user-section">
      <view class="user-info">
        <view class="user-avatar" @click="changeAvatar">
          <image v-if="userInfo.avatar" :src="userInfo.avatar" mode="aspectFill" />
          <view v-else class="default-avatar"></view>
        </view>
        <view class="user-details">
          <view v-if="!userInfo.isLogin" class="login-area">
            <button class="login-btn" @click="login">登录</button>
          </view>
          <view v-else class="user-logged">
            <text class="user-name">{{ userInfo.nickname }}</text>
            <text class="user-level">{{ userInfo.level }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 我的订单 -->
    <view class="order-section">
      <view class="section-header">
        <text class="section-title">我的订单</text>
        <view class="view-all" @click="navigateTo('/pages/profile/orders')">
          <text>查看全部</text>
          <text class="arrow">></text>
        </view>
      </view>
      <view class="order-stats">
        <view class="order-item" @click="navigateTo('/pages/profile/orders?status=pending')">
          <view class="order-icon">📄</view>
          <text class="order-label">待付款</text>
        </view>
        <view class="order-item" @click="navigateTo('/pages/profile/orders?status=processing')">
          <view class="order-icon">🖨️</view>
          <text class="order-label">准备中</text>
        </view>
        <view class="order-item" @click="navigateTo('/pages/profile/orders?status=shipping')">
          <view class="order-icon">📦</view>
          <text class="order-label">待发货</text>
        </view>
        <view class="order-item" @click="navigateTo('/pages/profile/orders?status=completed')">
          <view class="order-icon">✅</view>
          <text class="order-label">已完成</text>
        </view>
      </view>
    </view>

    <!-- 账户余额 -->
    <view class="balance-section">
      <view class="balance-info">
        <view class="balance-icon">💰</view>
        <view class="balance-content">
          <text class="balance-label">账户余额：</text>
          <text class="balance-amount">¥{{ userInfo.balance || 0 }}</text>
        </view>
        <button class="recharge-btn" @click="recharge">充值送现金</button>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" @click="navigateTo('/pages/store/store')">
        <view class="menu-icon">📋</view>
        <text class="menu-text">商城订单</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" @click="navigateTo('/pages/mailbox/mailbox')">
        <view class="menu-icon">📮</view>
        <text class="menu-text">代收信箱</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" @click="navigateTo('/pages/logistics/logistics')">
        <view class="menu-icon">📦</view>
        <text class="menu-text">回信查询</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" @click="navigateTo('/pages/profile/address')">
        <view class="menu-icon">✉️</view>
        <text class="menu-text">信件地址</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" @click="navigateTo('/pages/profile/coupons')">
        <view class="menu-icon">🎫</view>
        <text class="menu-text">我的优惠券</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" @click="navigateTo('/pages/gift/gift')">
        <view class="menu-icon">🔧</view>
        <text class="menu-text">邀请好友</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

      <!-- 退出登录 -->
      <button class="logout-btn" @click="logout">退出登录</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 用户信息
const userInfo = ref({
  id: '123456789',
  nickname: '信件爱好者',
  avatar: '',
  level: 'VIP会员',
  balance: 0,
  isLogin: false
})

// 登录
const login = () => {
  uni.navigateTo({
    url: '/pages/login/login'
  })
}

// 更换头像
const changeAvatar = () => {
  if (!userInfo.value.isLogin) {
    login()
    return
  }

  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      userInfo.value.avatar = res.tempFilePaths[0]
      uni.showToast({ title: '头像更新成功', icon: 'success' })
    }
  })
}

// 充值
const recharge = () => {
  if (!userInfo.value.isLogin) {
    login()
    return
  }

  uni.navigateTo({
    url: '/pages/profile/recharge'
  })
}

// 页面跳转
const navigateTo = (url: string) => {
  uni.navigateTo({ url })
}

// 联系客服
const contactService = () => {
  uni.showModal({
    title: '联系客服',
    content: '请拨打客服电话：400-123-4567',
    showCancel: false
  })
}

// 退出登录
const logout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清除用户数据
        uni.clearStorageSync()
        uni.showToast({ title: '已退出登录', icon: 'success' })
        
        // 跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/login'
          })
        }, 1500)
      }
    }
  })
}
</script>

<style scoped>
.profile-page {
  background: #f8f8f8;
  min-height: 100vh;
}

/* 用户信息区域 */
.user-section {
  background: #fff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.default-avatar {
  width: 100%;
  height: 100%;
  background: #e0e0e0;
  border-radius: 50%;
}

.user-details {
  flex: 1;
}

.login-area {
  display: flex;
  align-items: center;
}

.login-btn {
  background: #4A90E2;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.user-logged {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.user-level {
  font-size: 24rpx;
  color: #999;
}

/* 订单状态 */
.order-section {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.view-all {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #999;
  font-size: 24rpx;
}

.arrow {
  font-size: 20rpx;
}

.order-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.order-item {
  text-align: center;
  padding: 20rpx 0;
}

.order-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.order-label {
  font-size: 24rpx;
  color: #666;
}

/* 账户余额 */
.balance-section {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.balance-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.balance-icon {
  font-size: 40rpx;
}

.balance-content {
  flex: 1;
}

.balance-label {
  font-size: 28rpx;
  color: #666;
}

.balance-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b35;
}

.recharge-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 16rpx 24rpx;
  font-size: 24rpx;
}

/* 功能菜单 */
.menu-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 32rpx;
  width: 60rpx;
  margin-right: 20rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.menu-arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 退出登录 */
.logout-btn {
  background: #fff;
  color: #ff4d4f;
  border: 2rpx solid #ff4d4f;
  border-radius: 50rpx;
  height: 88rpx;
  font-size: 32rpx;
  margin: 0 20rpx;
}

.logout-btn:active {
  opacity: 0.8;
}
</style>
