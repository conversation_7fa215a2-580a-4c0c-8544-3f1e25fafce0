<template>
  <view class="profile-page">
    <view class="container">
      <!-- 用户信息 -->
      <view class="user-info">
        <view class="user-avatar" @click="changeAvatar">
          <image v-if="userInfo.avatar" :src="userInfo.avatar" mode="aspectFill" />
          <text v-else>{{ userInfo.nickname.charAt(0) }}</text>
        </view>
        <view class="user-details">
          <text class="user-name">{{ userInfo.nickname }}</text>
          <text class="user-level">{{ userInfo.level }}</text>
          <text class="user-id">ID: {{ userInfo.id }}</text>
        </view>
        <view class="user-actions">
          <button class="edit-btn" @click="editProfile">编辑</button>
        </view>
      </view>

      <!-- 数据统计 -->
      <view class="data-stats">
        <view class="stat-item" @click="viewLetters">
          <text class="stat-number">{{ userStats.letters }}</text>
          <text class="stat-label">寄出信件</text>
        </view>
        <view class="stat-item" @click="viewPostcards">
          <text class="stat-number">{{ userStats.postcards }}</text>
          <text class="stat-label">寄出明信片</text>
        </view>
        <view class="stat-item" @click="viewDiaries">
          <text class="stat-number">{{ userStats.diaries }}</text>
          <text class="stat-label">心语日记</text>
        </view>
        <view class="stat-item" @click="viewInvites">
          <text class="stat-number">{{ userStats.invites }}</text>
          <text class="stat-label">邀请好友</text>
        </view>
      </view>

      <!-- 功能菜单 */
      <view class="menu-section">
        <view class="menu-group">
          <view class="menu-item" @click="navigateTo('/pages/profile/orders')">
            <view class="menu-icon">📦</view>
            <text class="menu-text">我的订单</text>
            <text class="menu-arrow">></text>
          </view>
          <view class="menu-item" @click="navigateTo('/pages/profile/address')">
            <view class="menu-icon">📍</view>
            <text class="menu-text">收货地址</text>
            <text class="menu-arrow">></text>
          </view>
          <view class="menu-item" @click="navigateTo('/pages/profile/coupons')">
            <view class="menu-icon">🎫</view>
            <text class="menu-text">优惠券</text>
            <text class="menu-arrow">></text>
          </view>
        </view>

        <view class="menu-group">
          <view class="menu-item" @click="navigateTo('/pages/profile/feedback')">
            <view class="menu-icon">💬</view>
            <text class="menu-text">意见反馈</text>
            <text class="menu-arrow">></text>
          </view>
          <view class="menu-item" @click="navigateTo('/pages/profile/help')">
            <view class="menu-icon">❓</view>
            <text class="menu-text">帮助中心</text>
            <text class="menu-arrow">></text>
          </view>
          <view class="menu-item" @click="navigateTo('/pages/profile/about')">
            <view class="menu-icon">ℹ️</view>
            <text class="menu-text">关于我们</text>
            <text class="menu-arrow">></text>
          </view>
        </view>

        <view class="menu-group">
          <view class="menu-item" @click="navigateTo('/pages/profile/settings')">
            <view class="menu-icon">⚙️</view>
            <text class="menu-text">设置</text>
            <text class="menu-arrow">></text>
          </view>
        </view>
      </view>

      <!-- 退出登录 -->
      <button class="logout-btn" @click="logout">退出登录</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 用户信息
const userInfo = ref({
  id: '123456789',
  nickname: '信件爱好者',
  avatar: '',
  level: 'VIP会员'
})

// 用户统计
const userStats = ref({
  letters: 15,
  postcards: 8,
  diaries: 23,
  invites: 5
})

// 更换头像
const changeAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      userInfo.value.avatar = res.tempFilePaths[0]
      uni.showToast({ title: '头像更新成功', icon: 'success' })
    }
  })
}

// 编辑资料
const editProfile = () => {
  uni.navigateTo({
    url: '/pages/profile/edit'
  })
}

// 页面跳转
const navigateTo = (url: string) => {
  uni.navigateTo({ url })
}

// 查看信件
const viewLetters = () => {
  uni.navigateTo({
    url: '/pages/profile/letters'
  })
}

// 查看明信片
const viewPostcards = () => {
  uni.navigateTo({
    url: '/pages/profile/postcards'
  })
}

// 查看日记
const viewDiaries = () => {
  navigateTo('/pages/diary/diary')
}

// 查看邀请
const viewInvites = () => {
  navigateTo('/pages/gift/gift')
}

// 退出登录
const logout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清除用户数据
        uni.clearStorageSync()
        uni.showToast({ title: '已退出登录', icon: 'success' })
        
        // 跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/login'
          })
        }, 1500)
      }
    }
  })
}
</script>

<style scoped>
.profile-page {
  background: #f8f8f8;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

/* 用户信息 */
.user-info {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  color: #fff;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.user-avatar text {
  font-size: 48rpx;
  font-weight: bold;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-level {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.user-id {
  font-size: 22rpx;
  opacity: 0.6;
}

.edit-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid #fff;
  border-radius: 50rpx;
  padding: 16rpx 24rpx;
  font-size: 24rpx;
}

/* 数据统计 */
.data-stats {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #4A90E2;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 功能菜单 */
.menu-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.menu-group {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 32rpx;
  width: 60rpx;
  margin-right: 20rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.menu-arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 退出登录 */
.logout-btn {
  background: #fff;
  color: #ff4d4f;
  border: 2rpx solid #ff4d4f;
  border-radius: 50rpx;
  height: 88rpx;
  font-size: 32rpx;
  margin: 0 20rpx;
}

.logout-btn:active {
  opacity: 0.8;
}
</style>
