<template>
  <view class="postcard-page">
    <view class="container">
      <!-- 明信片模板选择 -->
      <view class="template-section">
        <view class="section-title">选择明信片模板</view>
        <scroll-view class="template-scroll" scroll-x>
          <view class="template-list">
            <view 
              v-for="(template, index) in templateList" 
              :key="index"
              class="template-item"
              :class="{ active: selectedTemplate === index }"
              @click="selectTemplate(index)"
            >
              <image :src="template.image" mode="aspectFill" />
              <text>{{ template.name }}</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 明信片预览 -->
      <view class="preview-section">
        <view class="section-title">明信片预览</view>
        <view class="postcard-preview">
          <view class="postcard-front" :style="{ backgroundImage: `url(${templateList[selectedTemplate]?.image})` }">
            <view class="front-content">
              <text class="preview-text">{{ postcardForm.frontText || '正面文字' }}</text>
            </view>
          </view>
          <view class="postcard-back">
            <view class="back-left">
              <text class="back-content">{{ postcardForm.backText || '背面内容...' }}</text>
            </view>
            <view class="back-right">
              <view class="address-area">
                <text class="address-text">{{ postcardForm.receiverName || '收件人' }}</text>
                <text class="address-text">{{ postcardForm.receiverAddress || '收件地址' }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 明信片内容编辑 -->
      <view class="content-section">
        <view class="section-title">编辑内容</view>
        <view class="form-item">
          <text class="label">正面文字</text>
          <input v-model="postcardForm.frontText" placeholder="请输入正面文字" class="input" />
        </view>
        <view class="form-item">
          <text class="label">背面内容</text>
          <textarea v-model="postcardForm.backText" placeholder="请输入背面内容..." class="textarea" />
        </view>
      </view>

      <!-- 收件人信息 -->
      <view class="receiver-section">
        <view class="section-title">收件人信息</view>
        <view class="form-item">
          <text class="label">收件人姓名</text>
          <input v-model="postcardForm.receiverName" placeholder="请输入收件人姓名" class="input" />
        </view>
        <view class="form-item">
          <text class="label">收件人地址</text>
          <textarea v-model="postcardForm.receiverAddress" placeholder="请输入详细地址" class="textarea" />
        </view>
        <view class="form-item">
          <text class="label">收件人电话</text>
          <input v-model="postcardForm.receiverPhone" placeholder="请输入收件人电话" class="input" type="number" />
        </view>
      </view>

      <!-- 提交按钮 -->
      <button class="submit-btn" @click="submitPostcard">确认寄出 ¥12</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// 明信片模板
const templateList = ref([
  { name: '风景', image: '/static/images/postcard1.jpg' },
  { name: '城市', image: '/static/images/postcard2.jpg' },
  { name: '花卉', image: '/static/images/postcard3.jpg' },
  { name: '动物', image: '/static/images/postcard4.jpg' }
])

// 选中的模板
const selectedTemplate = ref(0)

// 表单数据
const postcardForm = reactive({
  frontText: '',
  backText: '',
  receiverName: '',
  receiverAddress: '',
  receiverPhone: ''
})

// 选择模板
const selectTemplate = (index: number) => {
  selectedTemplate.value = index
}

// 提交明信片
const submitPostcard = () => {
  // 表单验证
  if (!postcardForm.receiverName) {
    uni.showToast({ title: '请输入收件人姓名', icon: 'none' })
    return
  }
  if (!postcardForm.receiverAddress) {
    uni.showToast({ title: '请输入收件人地址', icon: 'none' })
    return
  }
  if (!postcardForm.backText) {
    uni.showToast({ title: '请输入背面内容', icon: 'none' })
    return
  }

  // 显示确认弹窗
  uni.showModal({
    title: '确认寄出',
    content: '明信片寄送费用：¥12',
    success: (res) => {
      if (res.confirm) {
        // 这里应该调用API提交数据
        uni.showToast({ title: '寄出成功！', icon: 'success' })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    }
  })
}
</script>

<style scoped>
.postcard-page {
  background: #f8f8f8;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

.template-section,
.preview-section,
.content-section,
.receiver-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

/* 模板选择 */
.template-scroll {
  width: 100%;
}

.template-list {
  display: flex;
  gap: 20rpx;
  padding-bottom: 10rpx;
}

.template-item {
  flex-shrink: 0;
  width: 160rpx;
  text-align: center;
}

.template-item image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e0e0e0;
}

.template-item.active image {
  border-color: #4A90E2;
}

.template-item text {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

/* 明信片预览 */
.postcard-preview {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.postcard-front,
.postcard-back {
  width: 100%;
  height: 240rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
}

.postcard-front {
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.front-content {
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  padding: 20rpx;
  border-radius: 8rpx;
  text-align: center;
}

.preview-text {
  font-size: 28rpx;
}

.postcard-back {
  background: #fff;
  border: 1rpx solid #e0e0e0;
  display: flex;
}

.back-left {
  flex: 1;
  padding: 20rpx;
  border-right: 1rpx solid #e0e0e0;
}

.back-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.back-right {
  flex: 1;
  padding: 20rpx;
  display: flex;
  align-items: flex-end;
}

.address-area {
  width: 100%;
}

.address-text {
  display: block;
  font-size: 24rpx;
  color: #333;
  margin-bottom: 10rpx;
}

/* 表单样式 */
.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.textarea {
  width: 100%;
  min-height: 120rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.submit-btn {
  width: calc(100% - 40rpx);
  margin: 20rpx;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  height: 88rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.submit-btn:active {
  opacity: 0.8;
}
</style>
