<template>
  <view class="countdown-page">
    <view class="container">
      <!-- 倒计时设置 -->
      <view class="countdown-setting">
        <view class="setting-title">设置回家时间</view>
        <picker mode="date" :value="homeDate" @change="onDateChange">
          <view class="date-picker">
            <text>{{ homeDate || '请选择日期' }}</text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
        <picker mode="time" :value="homeTime" @change="onTimeChange">
          <view class="time-picker">
            <text>{{ homeTime || '请选择时间' }}</text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
        <button class="save-btn" @click="saveCountdown">保存设置</button>
      </view>

      <!-- 倒计时显示 -->
      <view v-if="countdownData" class="countdown-display">
        <view class="countdown-title">距离回家还有</view>
        <view class="countdown-time">
          <view class="time-item">
            <text class="time-number">{{ countdownData.days }}</text>
            <text class="time-unit">天</text>
          </view>
          <view class="time-item">
            <text class="time-number">{{ countdownData.hours }}</text>
            <text class="time-unit">时</text>
          </view>
          <view class="time-item">
            <text class="time-number">{{ countdownData.minutes }}</text>
            <text class="time-unit">分</text>
          </view>
          <view class="time-item">
            <text class="time-number">{{ countdownData.seconds }}</text>
            <text class="time-unit">秒</text>
          </view>
        </view>
        <view class="countdown-date">
          目标时间：{{ homeDate }} {{ homeTime }}
        </view>
      </view>

      <!-- 心情记录 -->
      <view class="mood-section">
        <view class="section-title">今日心情</view>
        <view class="mood-selector">
          <view 
            v-for="(mood, index) in moodList" 
            :key="index"
            class="mood-item"
            :class="{ active: selectedMood === index }"
            @click="selectMood(index)"
          >
            <text class="mood-emoji">{{ mood.emoji }}</text>
            <text class="mood-text">{{ mood.text }}</text>
          </view>
        </view>
        <textarea 
          v-model="moodNote" 
          placeholder="记录今天的心情..." 
          class="mood-textarea"
        />
        <button class="save-mood-btn" @click="saveMood">保存心情</button>
      </view>

      <!-- 历史记录 -->
      <view class="history-section">
        <view class="section-title">心情历史</view>
        <view v-if="moodHistory.length > 0" class="history-list">
          <view v-for="(record, index) in moodHistory" :key="index" class="history-item">
            <view class="history-date">{{ record.date }}</view>
            <view class="history-mood">
              <text class="history-emoji">{{ record.emoji }}</text>
              <text class="history-text">{{ record.text }}</text>
            </view>
            <view v-if="record.note" class="history-note">{{ record.note }}</view>
          </view>
        </view>
        <view v-else class="empty-history">
          <text>暂无心情记录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 回家日期和时间
const homeDate = ref('')
const homeTime = ref('')

// 倒计时数据
const countdownData = ref(null)

// 定时器
let timer = null

// 心情列表
const moodList = ref([
  { emoji: '😊', text: '开心' },
  { emoji: '😢', text: '难过' },
  { emoji: '😴', text: '困倦' },
  { emoji: '😤', text: '生气' },
  { emoji: '🤔', text: '思考' },
  { emoji: '😍', text: '兴奋' }
])

// 选中的心情
const selectedMood = ref(-1)

// 心情备注
const moodNote = ref('')

// 心情历史
const moodHistory = ref([
  {
    date: '2024-01-15',
    emoji: '😊',
    text: '开心',
    note: '今天收到了朋友的来信，很开心！'
  },
  {
    date: '2024-01-14',
    emoji: '🤔',
    text: '思考',
    note: '在思考人生的意义'
  }
])

// 日期选择
const onDateChange = (e: any) => {
  homeDate.value = e.detail.value
}

// 时间选择
const onTimeChange = (e: any) => {
  homeTime.value = e.detail.value
}

// 保存倒计时设置
const saveCountdown = () => {
  if (!homeDate.value || !homeTime.value) {
    uni.showToast({ title: '请选择完整的日期和时间', icon: 'none' })
    return
  }

  // 保存到本地存储
  uni.setStorageSync('homeDate', homeDate.value)
  uni.setStorageSync('homeTime', homeTime.value)
  
  startCountdown()
  uni.showToast({ title: '设置成功', icon: 'success' })
}

// 开始倒计时
const startCountdown = () => {
  if (!homeDate.value || !homeTime.value) return

  const updateCountdown = () => {
    const targetTime = new Date(`${homeDate.value} ${homeTime.value}`).getTime()
    const now = new Date().getTime()
    const diff = targetTime - now

    if (diff <= 0) {
      countdownData.value = { days: 0, hours: 0, minutes: 0, seconds: 0 }
      clearInterval(timer)
      return
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diff % (1000 * 60)) / 1000)

    countdownData.value = { days, hours, minutes, seconds }
  }

  updateCountdown()
  timer = setInterval(updateCountdown, 1000)
}

// 选择心情
const selectMood = (index: number) => {
  selectedMood.value = index
}

// 保存心情
const saveMood = () => {
  if (selectedMood.value === -1) {
    uni.showToast({ title: '请选择心情', icon: 'none' })
    return
  }

  const mood = moodList.value[selectedMood.value]
  const today = new Date().toISOString().split('T')[0]
  
  // 检查今天是否已经记录
  const existIndex = moodHistory.value.findIndex(item => item.date === today)
  
  const record = {
    date: today,
    emoji: mood.emoji,
    text: mood.text,
    note: moodNote.value
  }

  if (existIndex !== -1) {
    moodHistory.value[existIndex] = record
  } else {
    moodHistory.value.unshift(record)
  }

  // 重置表单
  selectedMood.value = -1
  moodNote.value = ''
  
  uni.showToast({ title: '保存成功', icon: 'success' })
}

onMounted(() => {
  // 从本地存储读取设置
  homeDate.value = uni.getStorageSync('homeDate') || ''
  homeTime.value = uni.getStorageSync('homeTime') || ''
  
  if (homeDate.value && homeTime.value) {
    startCountdown()
  }
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.countdown-page {
  background: #f8f8f8;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

/* 倒计时设置 */
.countdown-setting {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.setting-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.date-picker,
.time-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  background: #fafafa;
}

.picker-arrow {
  color: #ccc;
  font-size: 28rpx;
}

.save-btn {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #fff;
  border: none;
  border-radius: 8rpx;
  height: 80rpx;
  font-size: 28rpx;
}

/* 倒计时显示 */
.countdown-display {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  text-align: center;
  color: #fff;
}

.countdown-title {
  font-size: 32rpx;
  margin-bottom: 30rpx;
}

.countdown-time {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.time-item {
  text-align: center;
}

.time-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.time-unit {
  font-size: 24rpx;
  opacity: 0.8;
}

.countdown-date {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 心情记录 */
.mood-section,
.history-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.mood-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.mood-item {
  text-align: center;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #fafafa;
}

.mood-item.active {
  border-color: #4A90E2;
  background: #f0f7ff;
}

.mood-emoji {
  display: block;
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.mood-text {
  font-size: 24rpx;
  color: #666;
}

.mood-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background: #fafafa;
  margin-bottom: 20rpx;
}

.save-mood-btn {
  background: #52c41a;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  height: 80rpx;
  font-size: 28rpx;
}

/* 历史记录 */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.history-item {
  padding: 20rpx;
  background: #fafafa;
  border-radius: 8rpx;
}

.history-date {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.history-mood {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 10rpx;
}

.history-emoji {
  font-size: 32rpx;
}

.history-text {
  font-size: 28rpx;
  color: #333;
}

.history-note {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.empty-history {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}
</style>
