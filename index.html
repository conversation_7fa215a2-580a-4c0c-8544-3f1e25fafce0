<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>信件助理</title>
    <meta name="description" content="线上寄信，就是便捷">
    <meta name="keywords" content="信件,明信片,寄信,邮寄">
    <link rel="icon" href="/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }
        
        #app {
            height: 100%;
        }
        
        /* 加载动画 */
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #4A90E2;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-logo {
            width: 80px;
            height: 80px;
            background: #fff;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        .loading-text {
            color: #fff;
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .loading-desc {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        /* 隐藏加载动画 */
        .loading.hidden {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 加载动画 -->
        <div class="loading" id="loading">
            <div class="loading-logo">✉️</div>
            <div class="loading-text">信件助理</div>
            <div class="loading-desc">线上寄信，就是便捷</div>
        </div>
    </div>
    
    <script>
        // 页面加载完成后隐藏加载动画
        window.addEventListener('load', function() {
            setTimeout(function() {
                const loading = document.getElementById('loading');
                if (loading) {
                    loading.classList.add('hidden');
                    setTimeout(function() {
                        loading.style.display = 'none';
                    }, 300);
                }
            }, 1000);
        });
    </script>
    
    <script type="module" src="/src/main.ts"></script>
</body>
</html>
