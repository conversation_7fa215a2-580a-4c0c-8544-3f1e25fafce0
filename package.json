{"name": "mail-uniapp", "version": "1.0.0", "description": "信件助理小程序", "main": "main.js", "scripts": {"dev:mp-weixin": "uni build --watch --platform mp-weixin", "build:mp-weixin": "uni build --platform mp-weixin", "dev:h5": "uni build --watch --platform h5", "build:h5": "uni build --platform h5", "dev:app": "uni build --watch --platform app", "build:app": "uni build --platform app", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4020920241117001", "@dcloudio/uni-app-plus": "3.0.0-4020920241117001", "@dcloudio/uni-components": "3.0.0-4020920241117001", "@dcloudio/uni-h5": "3.0.0-4020920241117001", "@dcloudio/uni-mp-weixin": "3.0.0-4020920241117001", "vue": "^3.4.0", "pinia": "^2.1.0"}, "devDependencies": {"@dcloudio/types": "^3.4.0", "@dcloudio/uni-automator": "3.0.0-4020920241117001", "@dcloudio/uni-cli-shared": "3.0.0-4020920241117001", "@dcloudio/vite-plugin-uni": "3.0.0-4020920241117001", "@types/node": "^20.0.0", "@vue/tsconfig": "^0.4.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}, "uni-app": {"scripts": {}}}